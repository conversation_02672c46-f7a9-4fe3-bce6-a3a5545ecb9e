import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:signage/ui/widgets/image_widget.dart';

/// A widget that displays an image for a specified duration
class ImageTimerWidget extends StatefulWidget {
  /// The path to the image file
  final String filePath;

  /// The width of the widget
  final double? width;

  /// The height of the widget
  final double? height;

  /// The duration to display the image in seconds
  final int durationInSeconds;

  /// Callback when the timer completes
  final VoidCallback? onComplete;

  /// Creates an ImageTimerWidget
  const ImageTimerWidget({
    super.key,
    required this.filePath,
    this.width,
    this.height,
    required this.durationInSeconds,
    this.onComplete,
  });

  @override
  State<ImageTimerWidget> createState() => _ImageTimerWidgetState();
}

class _ImageTimerWidgetState extends State<ImageTimerWidget> {
  Timer? _timer;
  bool _hasTimerCompleted = false;

  @override
  void initState() {
    super.initState();
    debugPrint('ImageTimerWidget: initState for ${widget.filePath}');

    // Start the timer immediately in initState
    // This ensures consistent timing regardless of image loading
    _startTimer();

    // Pre-check if the file exists to handle errors early
    _checkFileExists();
  }

  // Check if the file exists to handle errors early
  Future<void> _checkFileExists() async {
    final file = File(widget.filePath);
    if (!await file.exists()) {
      debugPrint('ImageTimerWidget: file does not exist: ${widget.filePath}');
      // Call the completion callback immediately if the file doesn't exist
      if (widget.onComplete != null && !_hasTimerCompleted) {
        _hasTimerCompleted = true;
        widget.onComplete!();
      }
    }
  }

  @override
  void didUpdateWidget(ImageTimerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // If the file path or duration changed, reset the timer
    if (oldWidget.filePath != widget.filePath ||
        oldWidget.durationInSeconds != widget.durationInSeconds) {
      debugPrint('ImageTimerWidget: properties changed, resetting timer');
      _cancelTimer();
      _hasTimerCompleted = false;
      _startTimer();
      _checkFileExists();
    }
  }

  void _startTimer() {
    // Only start the timer if it hasn't been started yet
    if (_timer == null && !_hasTimerCompleted) {
      debugPrint('ImageTimerWidget: starting timer for ${widget.filePath} with duration ${widget.durationInSeconds} seconds');

      // Cancel any existing timer
      _cancelTimer();

      // Start a new timer
      _timer = Timer(Duration(seconds: widget.durationInSeconds), () {
        debugPrint('ImageTimerWidget: timer completed for ${widget.filePath}');

        // Set the flag to prevent multiple calls
        _hasTimerCompleted = true;

        // Call the completion callback if provided
        if (widget.onComplete != null) {
          widget.onComplete!();
        }
      });
    }
  }

  void _cancelTimer() {
    if (_timer != null) {
      debugPrint('ImageTimerWidget: canceling timer for ${widget.filePath}');
      _timer!.cancel();
      _timer = null;
    }
  }

  void _onImageLoaded() {
    debugPrint('ImageTimerWidget: image loaded for ${widget.filePath}');
    // No need to set state or start timer here as it's already started in initState
  }

  @override
  void dispose() {
    debugPrint('ImageTimerWidget: dispose for ${widget.filePath}');
    _cancelTimer();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ImageWidget(
      filePath: widget.filePath,
      width: widget.width,
      height: widget.height,
      onLoaded: _onImageLoaded,
      onError: (error) {
        debugPrint('ImageTimerWidget: error loading image: $error');

        // Call the completion callback on error if provided
        if (widget.onComplete != null && !_hasTimerCompleted) {
          _hasTimerCompleted = true;
          widget.onComplete!();
        }
      },
    );
  }
}
