# 📺 Digital Signage Flutter Player App

This document defines the **project structure**, **Flutter components**, **widgets**, and a **phased implementation roadmap** based on the attached workflow for a digital signage player app using Flutter. The app supports **Android, Windows, and Linux**, and is to be built with a coding agent like **Cursor.AI**.

---

## 🌐 Overview

**App Purpose**: Display multimedia campaigns (images/videos) based on schedules fetched from a CMS (via Supabase API).  
**Platforms**: Android, Windows, Linux  
**Deployment Mode**: Full-screen, all-monitor coverage  

---

## 🧱 Project Structure

```
signage/
├── lib/
│   ├── main.dart
│   ├── app.dart
│   ├── core/
│   │   ├── permissions/
│   │   ├── storage/
│   │   ├── network/
│   │   ├── models/
│   │   └── services/
│   ├── ui/
│   │   ├── screens/
│   │   ├── widgets/
│   │   └── themes/
│   └── utils/
├── assets/
│   ├── images/
│   │   ├── logo.png
├── pubspec.yaml
```

---

## 📦 Flutter Components & Widgets

| Component/Widget | Type | Description |
|------------------|------|-------------|
| `SplashScreen` | StatelessWidget | Logo screen on app start |
| `PermissionHandler` | Service | Handles permissions on Android |
| `RegistrationScreen` | StatefulWidget | User inputs 5-digit code |
| `PlayerScreen` | StatefulWidget | Main screen showing media |
| `CampaignController` | Controller | Manages playback state |
| `SupabaseService` | Service | Handles all Supabase API calls |
| `FileDownloader` | Service | Downloads media files |
| `FolderManager` | Utility | Manages signage folders |
| `VideoWidget` | StatefulWidget | Plays videos with event subscription |
| `ImageWidget` | StatelessWidget | Displays images for 8 seconds |
| `MultiMonitorHandler` | Utility | Detects all monitors and sets full-screen window size on Windows/Linux |

---

## 📦 Supabase Credentials

- URL : https://oatyudgnhndxrwwlbxsj.supabase.co
- ANON KEY : eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9hdHl1ZGduaG5keHJ3d2xieHNqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ4MDUwMDAsImV4cCI6MjA2MDM4MTAwMH0.FLeeCaNs_BAfMiPdi56SRYIS9XKk2nYIUS3ZmiyDGbE

---

## 🚧 Implementation Roadmap (Phased)

### Phase 1: App Initialization
- [ ] The entire app must be fullscreen without a titlebar
- [ ] Build splash screen with logo and a header text "Signage Player", sub header text "Version {app version}". Horizontal loading bar should be displayed at the bottom of the header text
- [ ] Detect platform (Android vs desktop)
- [ ] Android: Request permissions (read/write, internet, location, etc.)
- [ ] Create signage directory and subfolders and file creation structure as follows

  Android external storage / Windows '%APPDATA%/Local' / Linux '~/Documents'
  ├── signage/
  │   ├── content/
  │   ├── data/
  │   ├── temp/
  │   └── settings.json (don't create at this stage)

### Phase 2: Registration Flow
- [ ] From the splash screen,
  - [ ] if settings.json file doesn't exists in the signage folder, Show registration screen.
  - [ ] if settings.json file exists, read settings.json file and check its "code" field value. If its null, Show registration screen.
- [ ] Registration screen should display logo, header text "Signage Player" and Accept 5-digit registration code input and "Register" button
- [ ] On "Register" button click, fetch screen info from Supabase table screens (`screens.code` == 5-digit registration code & `screens.is_registered` == false)
  - [ ] If the above conditions are true: 
    - [ ] Update `screens.is_registered` = true while fetching `screens` record
    - [ ] Create `settings.json` and save screen info response data in `settings.json` in the signage folder
    - [ ] Register the player with supabase screen_registrations table (insert `screen_registrations.screen_id` == settings.id and `screen_registrations.registered_at` = device current date and time in UTC)
  - [ ] If the above conditions are false: Show error message "Invalid code" and clear input field

### Phase 3: Data Fetch & Media File Download
- [ ] Create separate data fetching and download components a suggested under "Flutter Components & Widgets" section above so that they can be used in background as well reused in other screens or components.
- [ ] After succesfull registration and saving settings.json file in the signage folder, fetch data and media files with progressbar:
  - [ ] Call Supabase API endpoints:
    - `fetch_media_files` RPC : save response in media.json in `temp/` folder
    - `campaigns` [select c.id, c.name, c.start_date, c.end_date, c.status from campaign_screens cs inner join campaigns c on c.id = cs.campaign_id and c.status = 'active' and {current date in UTC} between c.start_date and c.end_date where cs.screen_id = {settings.id}] : save response in campaings.json in `temp/` folder
    - `fetch_screen_campaigns_schedule` RPC : save response in schedule.json in `temp/` folder
  - [ ] Read media.json from `temp/` folder and Download all media files to `content/` using using supabase --> storage API. The name of the storage bucket is `medialibrary`. so parse the media.json "file_url" field value starting from `medialibrary/` till end as the file path, split the file path by "/" and use the following example to download the file to `content/` folder.
    - [ ] For example, if the file_url is `https://oatyudgnhndxrwwlbxsj.supabase.co/storage/v1/object/public/medialibrary/1c3a76e6d-2f37-4b41-b5bc-acc9f244b439/promo1.mp4`.
      - [ ] storage bucket name is `medialibrary`
      - [ ] The file_path is `1c3a76e6d-2f37-4b41-b5bc-acc9f244b439/promo1.mp4`
      - [ ] code example:
        
        final Uint8List file = await supabase.storage.from('medialibrary').download(file_path);

  - [ ] Show progressbar for download progress
  - [ ] When all the files are downloaded, move media.json, campaigns.json, schedule.json files from `temp/` to `data/` folder
  - [ ] Navigate to PlayerScreen
  

### Phase 4: Media Playback Engine
- [ ] On PlayerScreen load, read the `campaigns.json` and `schedule.json` files from the `data/` folder
- [ ] For each record in the campaigns data;

  ### Check if Current Date & Time is between Campaing Start & End Date
  - [ ] If the current device date and time between campaing[recordno].start_date and campaing[recordno].end_date then;
    - [ ] For each record in the schedule data;

      ### Check if Campaing ID match
      - [ ] If the schedule[recordno].campaignid == campaing[recordno].id then; 
        ### Media Campaing
        - [ ] If the schedule[recordno].campaign_type == 0 then;  

          ### Image Widget
          - [ ] If the schedule[recordno].name has Image extension (i.e. jpg, jpeg, png, bmp, gif, apng, svg, tiff) then
            - [ ] Prepare ImageWidget. The source path should be `{External Storage}/signage/content/{schedule[recordno].name}`. Get the player screen width and height and set the same as width and height of the ImageWidget. Fill the image content to fit the entire widget without maintaining aspect ratio.
            - [ ] Start an ContentTimer with 8 seconds interval
        
          ### Video Widget
          - [ ] Else If the schedule[recordno].name has video extension (i.e. mp4, webm) then;
            - [ ] Prepare VideoWidget. The source path should be `{External Storage}/signage/content/{schedule[recordno].name}`. Get the player screen width and height and set the same as width and height of the VideoWidget. Fill the video content to fit the entire widget without maintaining aspect ratio. Set loop = flase and play the video. Do no display any video controls.
            - [ ] Subscribe to VideoWidget `Ended` Event.

          ### Finish Playback & Move to Next Record/Content
          - [ ] When the ContentTimer elapse or VideoWidget 'Ended' event occurs, clear the timer as well as unsubscribe to 'Ended' events and then move to next schedule[recordno] record. While setting up widget for the next record, create new widget and load the content. Once the new widget is loaded/played, remove the old/previous widget. This way smooth transition from one content to next can be maintained. Please make sure to remove all the widget and it associated events references are removed properly. If current record is schedule[recordno] last record then move to first record and start loop.

        ### Slide Campaing
        - [ ] Else If the schedule[recordno].campaign_type == 1 then;

          ### Prepare & Add Slide Parent Container in the Player Screen
          - [ ] Create a parent container widget and its width = schedule[recordno].width and its height = schedule[recordno].height. Set its background color = schedule[recordno].bgcolor

          ### Prepare & Play Each Slide Widget
          - [ ] Read and load schedule[recordno].content field value in a JSON variable called ScheduleContent.
            - [ ] For each record in the ScheduleContent data;

              ### Prepare & Add Sub Container as placeholder for each slide widget
              - [ ] Create a sub container and set its Left position = ScheduleContent[recordno].x, Top position = ScheduleContent[recordno].y , width = ScheduleContent[recordno].width & height = ScheduleContent[recordno].height

              ### Prepare Playlist/Multimedia Widget to play Image/Video Widgets
              - [ ] If ScheduleContent[recordno].type == "playlist" then;
                - [ ] Load ScheduleContent[recordno].content.playlist in a playlist variable (array) called playlist[ScheduleContent.id];
                - [ ] For each record in the playlist[ScheduleContent.id];

                  ### Video Widget
                  - [ ] If playlist[ScheduleContent.id].[recordno].fileType == ""video/mp4" then;
                    - [ ] Prepare VideoWidget and add into the sub container. The source path should be `{External Storage}/signage/content/{playlist[ScheduleContent.id].[recordno].name}`. Get the 'sub container' width and height and set the same as width and height of the VideoWidget. Fill the video content to fit the entire widget without maintaining aspect ratio. Set loop = flase and play the video. Do no display any video controls. The VideoWidget should be assigned unique id so that later on when its finished playing content, it can be safely removed using the same id.
                    - [ ] Subscribe to VideoWidget `Ended` Event.

                  ### Image Widget
                  - [ ] Else If playlist[ScheduleContent.id].[recordno].fileType == ""video/jpg" or playlist[ScheduleContent.id].[recordno].fileType == ""video/jpeg" or playlist[ScheduleContent.id].[recordno].fileType == ""video/png" or playlist[ScheduleContent.id].[recordno].fileType == ""video/bmp" or playlist[ScheduleContent.id].[recordno].fileType == ""video/apng" or playlist[ScheduleContent.id].[recordno].fileType == ""video/tiff" then;
                    - [ ] Prepare ImageWidget and add into the sub container. The source path should be `{External Storage}/signage/content/{playlist[ScheduleContent.id].[recordno].name}`. Get the sub container width and height and set the same as width and height of the ImageWidget. Fill the image content to fit the entire widget without maintaining aspect ratio. The ImageWidget should be assigned unique id so that later on when its finished playing content, it can be safely removed using the same id.
                    - [ ] Start an ContentTimer with playlist[ScheduleContent.id].[recordno].duration value as interval

                  ### Finish Playback & Move to Next Record/Content in the Playlist/Multimedia Widget
                  - [ ] When the ContentTimer elapse or VideoWidget 'Ended' event occurs, clear the timer as well as unsubscribe to 'Ended' events and then move to next playlist[ScheduleContent.id] record. While setting up widget for the next record, create new widget and load the content. Once the new widget is loaded/played, remove the old/previous widget using the unique id. This way smooth transition from one content to next can be maintained. Please make sure to remove all the widget and it associated events references are removed properly.
          
          ### Move to Next Record
          - [ ] When Slide Campaign is running, the app will have to monitor when the Last record content (image or video) finished playing. When it's finihsed, move to next schedule[recordno] record. There might be a possible scenario where the slide has multiple Playlist/Multimedia widgets i.e. count of ScheduleContent[recordno].type == "playlist" > 1. In that case multiple playlist/multimedia widgets with multiple image/video contents would be playing concurrently. In such scenario, the app will have to keep track of which playlist/multimedia widget has finished playing last playlist[ScheduleContent.id] record and based on that it will have to move to next schedule[recordno] record. If current record is schedule[recordno] last record then move to first record and start loop.

      ### Campaing ID doesn't match
      - [ ] Else move to next record

  ### Current Date & Time is not between Campaing Start & End Date
  - [ ] Else move to next record


### Phase 5: Player Controller For Background Updates and status updates on the server

- [ ] Create a background service for player controller
- [ ] the service should start  and run in the background when the player screen is loaded. 
- [ ] Background service reads `settings.updatefrequency`
- [ ] Background service should start a timer using `updatefrequency` value. The value will be in the format of `HH:mm:ss` i.e. `00:00:30` means every 30 seconds. 
- [ ] On every timer lapse, check for internet connectivity and then update `last_ping_date` in `screens` table in the supabase using `screens.id` (screenId) from `settings.json`. It should update `screens.last_ping_at` = device current date and time in UTC using supabase `update` function. It should also retrieve device health such as CPU usage, Memory usage, Disk usage and update `screens.health` field with the JSON data.
- [ ] If internet is not available, skip the update check and wait for next timer lapse.
- [ ] Along with updatefrequency timer, the service should create another function to subscribe to supabase realtime `channels` and listen for `updateguid` changes in the `screens` table.
  - Below is the code to subscribe to supabase realtime `channels` and listen for `updateguid` changes in the `screens` table;
  ```dart
    final subscription = supabase
    .channel('public:screens')
    .onPostgresChanges(
      event: 'UPDATE',
      schema: 'public',
      table: 'screens',
      callback: (payload) {
        final newRow = payload.new;
        final oldRow = payload.old;
        final deviceScreenId = `screens.id` (screenId) from `settings.json`; // Fetch from settings.json

        // Only respond if this is the device's own screen_id
        if (newRow['id'] == deviceScreenId && newRow['updateguid'] != oldRow['updateguid']) {
          print('This device\'s screen info was updated!');
          // Trigger refresh, media sync, etc.
        }
      },
    )
    .subscribe();
  ```
  - [ ] When `updateguid` changes, the service should check for internet connectivity and then trigger a function to download new data and media files.
  - [ ] The function to download new data and media files should be same as the one used in Phase 3 using `DataFetchService` but in the background without any UI interaction.
  - [ ] On success, the function should replace the existing `data/` folder with the new `temp/` folder content and then restart the player.

### Phase 7: Player Device System Menu Handling
- [ ] Create a custom menu to handle system actions. the custom menu should be available during entire app lifecycle.
- [ ] The custom menu should be available on Android and Desktop platforms at the bottom right corner of the screen
- [ ] Detect system menu button press
  - [ ] On Android:
    - [ ] Prevent user to exit the app using Back button
    - [ ] Prevent user to exit the app using Recent Apps button
    - [ ] Prevent user to exit the app using Home button
    - [ ] Use Back button press to show custom menu
  - [ ] On Desktop:
    - [ ] Detect 'Ctrl+M' to show custom menu.

- [ ] Show custom menu with options "Restart Player", "Check for Updates" and "Exit"
- [ ] Handle menu actions appropriately
  - [ ] "Restart Player" should restart the player
  - [ ] "Check for Updates" should check for updates and download if available using `DataFetchService` with progress bar
  - [ ] "Exit" should exit the app

### Phase 8: Player Device Activity & Proof of Play Logging
- [ ] Create a service to handle activity and proof of play logging
- [ ] The service should be started when the player is started and stopped when the player is stopped. It should be called from respective places to log the activity.
- [ ] Following activity logging should be captured and logged into supabase `screen_activities` table:
  - [ ] Below is the screen_activities table structure;
    ```sql
    id int8
    screen_id uuid
    log_details text
    log_datetime timestamptz
    file_download_count int4
    total_file_download int4
    ```
  - [ ] The service should log the following activities:
    - [ ] Player Start
    - [ ] Each File Download. The log_details should contain file name and file size and current file count and total file count. i.e. `Downloaded file: promo1.mp4 (1.2 MB) - 1/10`. The current file count should be recorded in `screen_activities.file_download_count` and total file count should be recorded in `screen_activities.total_file_download`.
    - [ ] Player Data Update i.e. when player completes the data update using `DataFetchService`. The log_details should contain message `Data Update Completed`.
    - [ ] Any Error
  
- [ ] The service should log the following proof of play information in the supabase `proof_of_play` table:
  - [ ] Below is the screen_activities table structure;
    ```sql
    id int8
    screen_id uuid
    campaing_id uuid
    slide_id uuid
    media_id uuid
    log_datetime timestamptz
    ```
  - [ ] The service should log the following proof of play information:
    - [ ] When a slide is displayed, log the screen_id, campaing_id, slide_id and log_datetime
      - [ ] When a slide media (image/video) is displayed, log the screen_id, campaing_id, slide_id, media_id and log_datetime
    - [ ] Simple media widget (image/video played directly from schedule) is displayed, log the screen_id, campaing_id, media_id and log_datetime

### Phase 9: Player On-Off Feature

- [ ] Read the settings.startTime and settings.endTime. If both are not null, the app is supposed to display content between the Start Time and End Time.
  - [ ] The start time and end time is in "HH:mm:ss" format
  - [ ] When the app starts and player screen loads;
    - [ ] If the current device time is between the start time and end time, start displaying schedule items;
        - [ ] start a timer calculating difference between the end time and current time. When the timer elapse, stop displaying schedule items and show black screen.
        - [ ] When the stop timer elapse and black screen is displayed, start another timer by calculating difference between current time and the start time for the next day. When the timer elapse, start displaying schedule items.

    - [ ] If the current device time is before the start time, show black screen;
        - [ ] start a timer calculating difference between the current time and the start time. When the timer elapse, start displaying schedule items.
        - [ ] When the start timer elapse and schedule items are displayed, start another timer calculating difference between the end time and current time. When the timer elapse, stop displaying schedule items and show black screen.
        - [ ] When the stop timer elapse and black screen is displayed, start another timer by calculating difference between the current time and the start time for the next day. When the timer elapse, start displaying schedule items.

    - [ ] If the current device time is after the end time, show black screen;
        - [ ] start a timer calculating difference between the current time and the end time for the next day. When the timer elapse, start displaying schedule items.
    
    - [ ] If the settings.startTime and settings.endTime are null, start displaying schedule items without any timer.

    - [ ] If the settings.startTime and settings.endTime are not in valid "HH:mm:ss" format, continue displaying schedule items.

    - [ ] When the app is displaying content, if the screen is in sleep mode, it should wake up the screen.

    - [ ] When the app is displaying content, if the screen is locked, it should unlock the screen.

    - [ ] When the app is displaying content then always keep the screen alive.

### Phase 10: Multi-Monitor Support (Windows/Linux)
- [ ] Use `desktop_multi_window` or similar
- [ ] Detect all screens
- [ ] Extend player window across all screens

### Phase 11: Additional Features
- [ ] Duration field for Campaigns in case of image campaigns or API campaigns.
- [ ] Automatic app pakcage update
- [ ] Campaing Trigger options;
  - [ ] Immediate
  - [ ] On Proximity value received via serial communication
  - [ ] On Age & Gender value received via face recognition

---

## 🧠 Notes for Cursor.AI Agent

- All Supabase interactions should be encapsulated in a service class.
- Local storage access should be abstracted for platform compatibility.
- Ensure all JSON files (settings, campaigns, media, slides) follow strict schemas.
- Include debug logs for file access, download, and playback operations.
- Use `flutter_isolate` for background download/controller service if needed.
- Add logic to auto-restart the player loop upon updates.

---

## 🛠 Pubspec Dependencies (Partial)

```yaml
dependencies:
  flutter:
    sdk: flutter
  supabase_flutter: ^1.10.0
  permission_handler: ^11.0.1
  path_provider: ^2.1.1
  video_player: ^2.7.0
  desktop_multi_window: ^0.2.0
  flutter_isolate: ^2.0.4
  file_picker: ^6.1.1
  http: ^1.2.1
```

---

## ✅ Final Thoughts

With this plan, Cursor.AI or any coding agent can handle development phase by phase, ensuring logical flow, platform compatibility, and full-screen media rendering.