import 'package:flutter/material.dart';
import 'package:signage/ui/widgets/platform_video_widget.dart';

/// A widget for playing videos in the player
class VideoWidget extends StatefulWidget {
  /// The path to the video file
  final String filePath;

  /// The width of the widget
  final double? width;

  /// The height of the widget
  final double? height;

  /// Whether to loop the video
  final bool loop;

  /// Callback when the video is initialized
  final VoidCallback? onInitialized;

  /// Callback when the video playback ends
  final VoidCallback? onEnded;

  /// Callback when there's an error playing the video
  final Function(Object)? onError;

  /// Creates a VideoWidget
  const VideoWidget({
    super.key,
    required this.filePath,
    this.width,
    this.height,
    this.loop = false,
    this.onInitialized,
    this.onEnded,
    this.onError,
  });

  @override
  State<VideoWidget> createState() => _VideoWidgetState();
}

class _VideoWidgetState extends State<VideoWidget> {
  @override
  Widget build(BuildContext context) {
    return PlatformVideoWidget(
      filePath: widget.filePath,
      width: widget.width,
      height: widget.height,
      loop: widget.loop,
      onInitialized: widget.onInitialized,
      onEnded: widget.onEnded,
      onError: widget.onError,
    );
  }
}



