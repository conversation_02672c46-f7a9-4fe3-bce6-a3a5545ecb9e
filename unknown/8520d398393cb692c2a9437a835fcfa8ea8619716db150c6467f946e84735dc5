// ChatGPT's suggested angle calculation function. can you please convert this to dart code?
    function calculateNeedleAngle(min: number, max: number, val: number) {
      // Clamp the value between min and max
      const clampedValue = Math.max(min, Math.min(max, val));
      
      const range = max - min;
      

      const angle = -90 + (normalized * 180);
      
      return angle; // in degrees
    }

    
