import 'package:flutter/material.dart';
import 'package:signage/core/handlers/system_menu_handler.dart';
import 'package:signage/core/platform/android_system_button_handler.dart';
import 'package:signage/ui/screens/data_loading_screen.dart';
import 'package:signage/utils/platform_utils.dart';

/// Controller for the system menu
class SystemMenuController {
  // Singleton instance
  static final SystemMenuController _instance = SystemMenuController._internal();
  factory SystemMenuController() => _instance;
  SystemMenuController._internal();

  // Global key for navigation
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  // System menu handlers
  SystemMenuHandler? _systemMenuHandler;
  AndroidSystemButtonHandler? _androidSystemButtonHandler;

  // State
  bool _isInitialized = false;
  VoidCallback? _onShowMenu;
  BuildContext? _context;

  /// Initialize the controller
  void initialize(BuildContext context, {required VoidCallback onShowMenu}) {
    if (_isInitialized) {
      return;
    }

    _isInitialized = true;
    _onShowMenu = onShowMenu;
    _context = context;

    // Initialize platform-specific handlers
    _initializeHandlers();
  }

  /// Initialize platform-specific handlers
  void _initializeHandlers() {
    // Initialize system menu handler for keyboard shortcuts
    _systemMenuHandler = SystemMenuHandler(
      onShowMenu: _handleShowMenu,
    );
    _systemMenuHandler?.initialize(_context!);

    // Initialize Android system button handler
    if (PlatformUtils.isAndroid) {
      _androidSystemButtonHandler = AndroidSystemButtonHandler(
        onBackButtonPressed: _handleShowMenu,
      );
    }
  }

  /// Handle show menu request
  void _handleShowMenu() {
    if (_onShowMenu != null) {
      _onShowMenu!();
    }
  }

  /// Restart the player
  void restartPlayer(BuildContext context) {
    // Use the navigator key to navigate to the data loading screen
    if (navigatorKey.currentState != null) {
      navigatorKey.currentState!.pushReplacement(
        MaterialPageRoute(
          builder: (context) => const DataLoadingScreen(),
        ),
      );
    } else {
      debugPrint('SystemMenuController: Navigator key is not available');
      // Fallback to try using the provided context
      try {
        Navigator.of(context, rootNavigator: true).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const DataLoadingScreen(),
          ),
        );
      } catch (e) {
        debugPrint('SystemMenuController: Failed to navigate: $e');
      }
    }
  }

  /// Dispose the controller
  void dispose() {
    // Dispose system menu handler
    _systemMenuHandler?.dispose();

    // Dispose Android system button handler
    if (PlatformUtils.isAndroid) {
      _androidSystemButtonHandler?.dispose();
    }

    // Reset state
    _isInitialized = false;
    _onShowMenu = null;
    _context = null;
  }
}
