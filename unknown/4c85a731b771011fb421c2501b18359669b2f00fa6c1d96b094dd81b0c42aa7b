import 'package:flutter/services.dart';
import 'package:flutter/material.dart';

/// A handler for Android system buttons
class AndroidSystemButtonHandler {
  /// Method channel for communication with native Android code
  static const MethodChannel _channel = MethodChannel('com.app.signage/system_buttons');
  
  /// Callback for when the back button is pressed
  final VoidCallback onBackButtonPressed;
  
  /// Constructor
  AndroidSystemButtonHandler({
    required this.onBackButtonPressed,
  }) {
    _initializeMethodChannel();
  }
  
  /// Initialize the method channel
  void _initializeMethodChannel() {
    _channel.setMethodCallHandler(_handleMethodCall);
  }
  
  /// Handle method calls from native code
  Future<dynamic> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'onBackButtonPressed':
        onBackButtonPressed();
        return true; // Prevent default behavior
      default:
        return false;
    }
  }
  
  /// Dispose the handler
  void dispose() {
    _channel.setMethodCallHandler(null);
  }
}
