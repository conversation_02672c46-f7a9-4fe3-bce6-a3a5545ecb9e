import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:signage/utils/platform_utils.dart';

/// Monitor information class
class MonitorInfo {
  final int id;
  final double x;
  final double y;
  final double width;
  final double height;
  final bool isPrimary;

  MonitorInfo({
    required this.id,
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    this.isPrimary = false,
  });

  @override
  String toString() {
    return 'Monitor $id: ($x, $y) ${width}x$height, isPrimary: $isPrimary';
  }
}

/// Class to handle multi-monitor setup and fullscreen mode
class MultiMonitorHandler {
  /// List of connected monitors
  static List<MonitorInfo> _monitors = [];

  /// Get the list of connected monitors
  static List<MonitorInfo> get monitors => _monitors;

  /// Total width of all monitors
  static double _totalWidth = 0;

  /// Total height of all monitors
  static double _totalHeight = 0;

  /// Get the total width of all monitors
  static double get totalWidth => _totalWidth;

  /// Get the total height of all monitors
  static double get totalHeight => _totalHeight;

  /// Initialize the multi-monitor handler
  static Future<void> initialize() async {
    if (PlatformUtils.isAndroid) {
      // On Android, set fullscreen mode but don't force orientation
      // We only set the UI style to hide status and navigation bars
      SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        systemNavigationBarColor: Colors.transparent,
      ));
      return;
    }

    // For desktop platforms, detect monitors
    if (PlatformUtils.isDesktop) {
      await _detectMonitors();
      _calculateTotalDimensions();
      print('Total dimensions: $_totalWidth x $_totalHeight');
    }
  }

  /// Detect connected monitors
  static Future<void> _detectMonitors() async {
    _monitors = [];

    // This is a placeholder for actual monitor detection
    // In a real implementation, we would use platform channels to get monitor information
    // For now, we'll simulate a single monitor with standard dimensions

    // Simulate primary monitor
    _monitors.add(MonitorInfo(
      id: 0,
      x: 0,
      y: 0,
      width: 1920,
      height: 1080,
      isPrimary: true,
    ));

    // Uncomment to simulate a second monitor
    // _monitors.add(MonitorInfo(
    //   id: 1,
    //   x: 1920,
    //   y: 0,
    //   width: 1920,
    //   height: 1080,
    //   isPrimary: false,
    // ));

    print('Detected ${_monitors.length} monitors:');
    for (var monitor in _monitors) {
      print(monitor);
    }
  }

  /// Calculate total dimensions based on monitor layout
  static void _calculateTotalDimensions() {
    _totalWidth = 0;
    _totalHeight = 0;

    for (var monitor in _monitors) {
      if (monitor.width > monitor.height) {
        // Landscape orientation
        _totalWidth += monitor.width;
        if (monitor.height > _totalHeight) {
          _totalHeight = monitor.height;
        }
      } else if (monitor.width < monitor.height) {
        // Portrait orientation
        if (monitor.width > _totalWidth) {
          _totalWidth = monitor.width;
        }
        _totalHeight += monitor.height;
      } else {
        // Square monitor
        if (monitor.width > _totalWidth) {
          _totalWidth = monitor.width;
        }
        if (monitor.height > _totalHeight) {
          _totalHeight = monitor.height;
        }
      }
    }

    // Ensure we have at least some dimensions
    if (_totalWidth <= 0) _totalWidth = 1920;
    if (_totalHeight <= 0) _totalHeight = 1080;
  }
}
