import 'dart:convert';

/// Model class representing a screen activity log entry in the Supabase 'screen_activities' table
class ScreenActivity {
  final int? id;
  final String screenId;
  final String logDetails;
  final DateTime logDatetime;
  final int? fileDownloadCount;
  final int? totalFileDownload;

  ScreenActivity({
    this.id,
    required this.screenId,
    required this.logDetails,
    required this.logDatetime,
    this.fileDownloadCount,
    this.totalFileDownload,
  });

  /// Create a ScreenActivity from a JSON map
  factory ScreenActivity.fromJson(Map<String, dynamic> json) {
    return ScreenActivity(
      id: json['id'],
      screenId: json['screen_id']?.toString() ?? '',
      logDetails: json['log_details'] ?? '',
      logDatetime: json['log_datetime'] != null
          ? DateTime.parse(json['log_datetime'])
          : DateTime.now().toUtc(),
      fileDownloadCount: json['file_download_count'],
      totalFileDownload: json['total_file_download'],
    );
  }

  /// Convert ScreenActivity to a JSON map
  Map<String, dynamic> toJson() {
    return {
      // Exclude id field as it's an identity column in Supabase
      'screen_id': screenId,
      'log_details': logDetails,
      'log_datetime': logDatetime.toIso8601String(),
      if (fileDownloadCount != null) 'file_download_count': fileDownloadCount,
      if (totalFileDownload != null) 'total_file_download': totalFileDownload,
    };
  }

  @override
  String toString() {
    return 'ScreenActivity(id: $id, screenId: $screenId, logDetails: $logDetails, logDatetime: $logDatetime)';
  }
}
