package com.app.signage

import android.app.Activity
import android.app.ActivityManager
import android.app.admin.DevicePolicyManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import android.view.WindowInsets
import android.view.WindowInsetsController
import android.view.WindowManager
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity : FlutterActivity() {
    private val CHANNEL = "com.app.signage/system_buttons"

    // Device policy manager for lock task mode
    private lateinit var devicePolicyManager: DevicePolicyManager
    private lateinit var activityManager: ActivityManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Set fullscreen flags and prevent screenshots/recordings
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        // Uncomment the following line if you want to prevent screenshots and screen recordings
        // window.addFlags(WindowManager.LayoutParams.FLAG_SECURE)

        // Initialize device policy manager and activity manager
        devicePolicyManager = getSystemService(Context.DEVICE_POLICY_SERVICE) as DevicePolicyManager
        activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager

        // Hide system UI (status bar and navigation bar)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // For Android 11 (API level 30) and above
            window.setDecorFitsSystemWindows(false)
            window.insetsController?.let {
                it.hide(WindowInsets.Type.statusBars() or WindowInsets.Type.navigationBars())
                it.systemBarsBehavior = WindowInsetsController.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
            }
        } else {
            // For Android 10 (API level 29) and below
            @Suppress("DEPRECATION")
            window.decorView.systemUiVisibility = (
                View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                or View.SYSTEM_UI_FLAG_FULLSCREEN
            )
        }

        // Set empty task description to make app less visible in recent apps
        setEmptyTaskDescription()

        // Set up task locking if available
        setupTaskLocking()
    }

    // Set an empty task description to make the app less visible in recent apps
    private fun setEmptyTaskDescription() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            try {
                // Create an empty task description
                val taskDesc = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                    ActivityManager.TaskDescription(null, 0)
                } else {
                    @Suppress("DEPRECATION")
                    ActivityManager.TaskDescription(null, null, 0)
                }

                // Set the task description
                setTaskDescription(taskDesc)
                println("Set empty task description")
            } catch (e: Exception) {
                println("Error setting empty task description: ${e.message}")
            }
        }
    }

    private fun setupTaskLocking() {
        // Instead of using lock task mode which requires user interaction,
        // we'll use a combination of other techniques to keep the app in foreground

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            try {
                // Move task to front
                activityManager.moveTaskToFront(taskId, 0)

                // Set app as home app if possible
                val intent = Intent(Intent.ACTION_MAIN)
                intent.addCategory(Intent.CATEGORY_HOME)
                intent.setPackage(packageName)

                // Set as default home app programmatically if possible
                // Note: This won't work on all devices due to security restrictions
                try {
                    val roleManager = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                        getSystemService(Context.ROLE_SERVICE) as android.app.role.RoleManager
                    } else {
                        null
                    }

                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q && roleManager != null) {
                        if (roleManager.isRoleAvailable(android.app.role.RoleManager.ROLE_HOME)) {
                            println("Home role is available")
                        }
                    }
                } catch (e: Exception) {
                    println("Error setting app as home app: ${e.message}")
                }

                println("Task locking setup completed without using standard lock task mode")
            } catch (e: Exception) {
                println("Error setting up task locking: ${e.message}")
            }
        }
    }

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // Set up method channel
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "exitApp" -> {
                    // Force close the app
                    finishAffinity()
                    // For API level 16 and above
                    android.os.Process.killProcess(android.os.Process.myPid())
                    result.success(true)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        if (hasFocus) {
            hideSystemUI()
        }
    }

    // Override onKeyDown to handle back button press and home button
    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        when (keyCode) {
            KeyEvent.KEYCODE_BACK -> {
                // Notify Flutter about back button press
                MethodChannel(flutterEngine!!.dartExecutor.binaryMessenger, CHANNEL)
                    .invokeMethod("onBackButtonPressed", null)
                return true // Prevent default behavior
            }
            KeyEvent.KEYCODE_HOME -> {
                // Prevent home button from working
                activityManager.moveTaskToFront(taskId, 0)
                return true // Prevent default behavior
            }
            KeyEvent.KEYCODE_APP_SWITCH -> {
                // Prevent recent apps from showing
                activityManager.moveTaskToFront(taskId, 0)
                return true // Prevent default behavior
            }
            KeyEvent.KEYCODE_MENU -> {
                // Prevent menu button from working
                return true // Prevent default behavior
            }
            else -> return super.onKeyDown(keyCode, event)
        }
    }

    // Override onKeyUp to prevent recent apps and home buttons from working
    override fun onKeyUp(keyCode: Int, event: KeyEvent?): Boolean {
        when (keyCode) {
            KeyEvent.KEYCODE_APP_SWITCH -> {
                // Prevent recent apps from showing
                activityManager.moveTaskToFront(taskId, 0)
                return true
            }
            KeyEvent.KEYCODE_HOME -> {
                // Prevent home button from working
                activityManager.moveTaskToFront(taskId, 0)
                return true
            }
            KeyEvent.KEYCODE_MENU -> {
                // Prevent menu button from working
                return true
            }
            else -> return super.onKeyUp(keyCode, event)
        }
    }

    // Override task description to prevent app from being shown in recent apps
    override fun onResume() {
        super.onResume()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            // Move task to front
            activityManager.moveTaskToFront(taskId, 0)
        }

        // Re-enable lock task mode if it was disabled
        setupTaskLocking()

        // Ensure system UI is hidden
        hideSystemUI()
    }

    // Override onUserLeaveHint to prevent home button from working
    override fun onUserLeaveHint() {
        // Instead of calling super.onUserLeaveHint(), we bring our task back to front
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            activityManager.moveTaskToFront(taskId, 0)
        }

        // Re-enable lock task mode
        setupTaskLocking()
    }

    // Override onPause to prevent app from being paused
    override fun onPause() {
        super.onPause()

        // Bring our task back to front
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            activityManager.moveTaskToFront(taskId, 0)
        }
    }

    // Override onStop to prevent app from being stopped
    override fun onStop() {
        super.onStop()

        // Restart the activity if it's stopped
        val intent = Intent(this, MainActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        startActivity(intent)
    }

    // Override onDestroy to restart the app if it's destroyed
    override fun onDestroy() {
        super.onDestroy()

        // Only restart if not finishing due to explicit exit
        if (!isFinishing) {
            val intent = Intent(this, MainActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            startActivity(intent)
        }
    }

    // Override startActivity to prevent certain intents from launching
    override fun startActivity(intent: Intent) {
        // Check if this is a home intent or recent apps intent
        if (intent.categories?.contains(Intent.CATEGORY_HOME) == true ||
            intent.action == Intent.ACTION_MAIN && intent.categories?.contains(Intent.CATEGORY_HOME) == true) {
            // Block home intent
            return
        }
        super.startActivity(intent)
    }

    private fun hideSystemUI() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // For Android 11 (API level 30) and above
            window.setDecorFitsSystemWindows(false)
            window.insetsController?.let {
                it.hide(WindowInsets.Type.statusBars() or WindowInsets.Type.navigationBars())
                it.systemBarsBehavior = WindowInsetsController.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
            }
        } else {
            // For Android 10 (API level 29) and below
            @Suppress("DEPRECATION")
            window.decorView.systemUiVisibility = (
                View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                or View.SYSTEM_UI_FLAG_FULLSCREEN
            )
        }
    }
}
