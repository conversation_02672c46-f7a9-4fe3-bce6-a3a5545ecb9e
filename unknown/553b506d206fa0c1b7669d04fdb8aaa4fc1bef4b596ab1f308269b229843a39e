import 'package:flutter_test/flutter_test.dart';
import 'package:signage/core/controllers/api_data_controller.dart';

void main() {
  group('ApiDataController', () {
    late ApiDataController controller;

    setUp(() {
      controller = ApiDataController(
        apiUrl: 'https://example.com/api',
        apiResponseData: [
          {'name': 'Record 1'},
          {'name': 'Record 2'},
          {'name': 'Record 3'},
        ],
        apiDataPreviewDuration: 5,
        onComplete: () {},
        totalRecords: 3,
      );
    });

    test('should initialize with correct values', () {
      expect(controller.apiResponseData.length, 3);
      expect(controller.apiDataPreviewDuration, 5);
      expect(controller.totalRecords, 3);
      expect(controller.currentRecordIndex, 0);
    });

    test('should get current record data', () {
      final data = controller.getCurrentRecordData();
      expect(data, {'name': 'Record 1'});
    });

    test('should advance to next record', () {
      controller.advanceToNextRecord();
      expect(controller.currentRecordIndex, 1);

      final data = controller.getCurrentRecordData();
      expect(data, {'name': 'Record 2'});
    });

    test('should register and unregister video widgets', () {
      // We can't directly test private fields, so we'll test the behavior instead
      controller.registerVideoWidget('video1');
      controller.registerVideoWidget('video2');

      // Create a new controller with apiDataPreviewDuration = 0
      // to test video completion behavior
      bool completeCalled = false;
      final testController = ApiDataController(
        apiUrl: 'https://example.com/api',
        apiResponseData: [{'test': 'data'}],
        apiDataPreviewDuration: 0,
        onComplete: () {
          completeCalled = true;
        },
        totalRecords: 1,
      );

      // Register videos
      testController.registerVideoWidget('video1');
      testController.registerVideoWidget('video2');

      // Complete one video - should not advance yet
      testController.notifyVideoComplete('video1');
      expect(completeCalled, false);

      // Unregister one video
      testController.unregisterVideoWidget('video2');

      // Now complete the remaining video - should advance
      testController.notifyVideoComplete('video1');
      expect(completeCalled, true);
    });

    test('should handle video completion', () {
      bool completeCalled = false;

      controller = ApiDataController(
        apiUrl: 'https://example.com/api',
        apiResponseData: [
          {'name': 'Record 1'},
          {'name': 'Record 2'},
        ],
        apiDataPreviewDuration: 0, // Use natural video duration
        onComplete: () {
          completeCalled = true;
        },
        totalRecords: 2,
      );

      controller.registerVideoWidget('video1');
      controller.notifyVideoComplete('video1');

      // Should advance to next record when all videos complete
      expect(controller.currentRecordIndex, 1);
      expect(completeCalled, false);

      // Register a new video for the second record
      controller.registerVideoWidget('video2');
      controller.notifyVideoComplete('video2');

      // Should call onComplete when all records are done
      expect(completeCalled, true);
    });

    test('should dispose properly', () {
      // Create a new controller
      bool completeCalled = false;
      final testController = ApiDataController(
        apiUrl: 'https://example.com/api',
        apiResponseData: [{'test': 'data'}],
        apiDataPreviewDuration: 0,
        onComplete: () {
          completeCalled = true;
        },
        totalRecords: 1,
      );

      // Register a video
      testController.registerVideoWidget('video1');

      // Dispose the controller
      testController.dispose();

      // After disposal, operations should have no effect
      testController.notifyVideoComplete('video1');
      testController.advanceToNextRecord();

      // onComplete should not have been called
      expect(completeCalled, false);
    });
  });
}
