import 'package:flutter/material.dart';
import 'package:signage/core/services/app_exit_service.dart';
import 'package:signage/core/services/data_fetch_service.dart';
import 'package:signage/core/services/cursor_manager.dart';
import 'package:signage/utils/platform_utils.dart';

/// A widget that displays a system menu for the player
/// This menu is shown when the user presses the back button on Android
/// or Ctrl+M on desktop platforms
class SystemMenuWidget extends StatefulWidget {
  /// Whether the menu is visible
  final bool isMenuVisible;

  /// Callback for when the menu visibility changes
  final Function(bool isVisible)? onMenuVisibilityChanged;

  /// Callback for when the player should be restarted
  final VoidCallback? onRestartPlayer;

  const SystemMenuWidget({
    super.key,
    this.isMenuVisible = false,
    this.onMenuVisibilityChanged,
    this.onRestartPlayer,
  });

  @override
  State<SystemMenuWidget> createState() => _SystemMenuWidgetState();
}

class _SystemMenuWidgetState extends State<SystemMenuWidget> {
  bool _isUpdating = false;
  double _updateProgress = 0.0;
  String _updateMessage = '';

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Full menu when visible
        if (widget.isMenuVisible)
          Positioned.fill(
            child: _buildFullMenu(),
          ),

        // Update progress overlay
        if (_isUpdating)
          Positioned.fill(
            child: _buildUpdateProgressOverlay(),
          ),
      ],
    );
  }

  /// Build the full menu overlay
  Widget _buildFullMenu() {
    return GestureDetector(
      onTap: _hideMenu,
      child: Container(
        color: Colors.black.withAlpha(179),
        child: Center(
          child: Material(
            elevation: 8,
            borderRadius: BorderRadius.circular(8),
            child: Container(
              width: 300,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text(
                    'System Menu',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildMenuOption(
                    icon: Icons.refresh,
                    label: 'Restart Player',
                    onTap: _restartPlayer,
                  ),
                  const Divider(),
                  _buildMenuOption(
                    icon: Icons.system_update,
                    label: 'Check for Updates',
                    onTap: _checkForUpdates,
                  ),
                  const Divider(),
                  _buildMenuOption(
                    icon: Icons.exit_to_app,
                    label: 'Exit',
                    onTap: _exitApp,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Build a menu option
  Widget _buildMenuOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            Icon(icon, size: 24),
            const SizedBox(width: 16),
            Text(
              label,
              style: const TextStyle(fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }

  /// Build the update progress overlay
  Widget _buildUpdateProgressOverlay() {
    return Container(
      color: Colors.black.withAlpha(204),
      child: Center(
        child: Material(
          elevation: 8,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            width: 300,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'Checking for Updates',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  _updateMessage,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                LinearProgressIndicator(
                  value: _updateProgress,
                  backgroundColor: Colors.grey[300],
                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
                  minHeight: 10,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Hide the menu
  void _hideMenu() {
    // Hide cursor when menu is dismissed on desktop platforms
    if (PlatformUtils.isDesktop) {
      CursorManager.instance.hideCursor();
    }

    if (widget.onMenuVisibilityChanged != null) {
      widget.onMenuVisibilityChanged!(false);
    }
  }

  /// Restart the player
  void _restartPlayer() {
    // First hide the menu
    _hideMenu();

    // Use a small delay to ensure the menu is hidden before restarting
    Future.delayed(const Duration(milliseconds: 100), () {
      if (widget.onRestartPlayer != null) {
        widget.onRestartPlayer!();
      }
    });
  }

  /// Check for updates
  Future<void> _checkForUpdates() async {
    _hideMenu();
    setState(() {
      _isUpdating = true;
      _updateProgress = 0.0;
      _updateMessage = 'Preparing to check for updates...';
    });

    final dataFetchService = DataFetchService(
      onProgress: (progress, message) {
        setState(() {
          _updateProgress = progress;
          _updateMessage = message;
        });
      },
      onComplete: () {
        setState(() {
          _isUpdating = false;
        });

        // Show a success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Update completed successfully. Restarting player...'),
            duration: Duration(seconds: 3),
          ),
        );

        // Restart the player after a short delay
        Future.delayed(const Duration(seconds: 3), () {
          if (widget.onRestartPlayer != null) {
            widget.onRestartPlayer!();
          }
        });
      },
      onError: (error) {
        setState(() {
          _isUpdating = false;
        });

        // Show an error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Update failed: $error'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      },
    );

    await dataFetchService.fetchAllData();
  }

  /// Exit the app
  void _exitApp() {
    // Hide the menu first
    _hideMenu();

    // Use a small delay to ensure the menu is hidden before exiting
    Future.delayed(const Duration(milliseconds: 100), () {
      // Use the AppExitService to properly exit the app
      AppExitService.exitApp();
    });
  }
}
