import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:signage/ui/widgets/system_menu_widget.dart';

void main() {
  testWidgets('SystemMenuWidget shows menu button after delay', (WidgetTester tester) async {
    // Build our widget
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: SystemMenuWidget(
            onRestartPlayer: () {},
            onMenuVisibilityChanged: (_) {},
          ),
        ),
      ),
    );

    // Initially, the menu button should not be visible
    expect(find.byType(GestureDetector), findsNothing);

    // Advance time to simulate the delay
    await tester.pump(const Duration(seconds: 6));

    // Now the menu button should be visible
    expect(find.byType(GestureDetector), findsOneWidget);
  });

  testWidgets('SystemMenuWidget shows menu when isMenuVisible is true', (WidgetTester tester) async {
    // Build our widget with isMenuVisible = true
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: SystemMenuWidget(
            isMenuVisible: true,
            onRestartPlayer: () {},
            onMenuVisibilityChanged: (_) {},
          ),
        ),
      ),
    );

    // The full menu should be visible
    expect(find.text('System Menu'), findsOneWidget);
    expect(find.text('Restart Player'), findsOneWidget);
    expect(find.text('Check for Updates'), findsOneWidget);
    expect(find.text('Exit'), findsOneWidget);
  });

  testWidgets('SystemMenuWidget calls onMenuVisibilityChanged when menu button is tapped', (WidgetTester tester) async {
    bool menuVisible = false;

    // Build our widget
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: SystemMenuWidget(
            isMenuVisible: menuVisible,
            onMenuVisibilityChanged: (isVisible) {
              menuVisible = isVisible;
            },
            onRestartPlayer: () {},
          ),
        ),
      ),
    );

    // Advance time to simulate the delay
    await tester.pump(const Duration(seconds: 6));

    // Tap the menu button
    await tester.tap(find.byType(GestureDetector));
    await tester.pump();

    // Verify that onMenuVisibilityChanged was called
    expect(menuVisible, isTrue);
  });
}
