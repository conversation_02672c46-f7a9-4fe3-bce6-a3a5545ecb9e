import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:signage/utils/platform_utils.dart';
import 'package:signage/core/services/cursor_manager.dart';

/// A handler for system menu events
class SystemMenuHandler {
  /// Callback for when the system menu should be shown
  final VoidCallback onShowMenu;

  /// Constructor
  SystemMenuHandler({
    required this.onShowMenu,
  });

  // Track control key state
  bool _isControlPressed = false;

  /// Initialize the handler
  void initialize(BuildContext context) {
    // Set up keyboard shortcuts for desktop platforms
    if (PlatformUtils.isDesktop) {
      _setupKeyboardShortcuts(context);
    }
  }

  /// Set up keyboard shortcuts for desktop platforms
  void _setupKeyboardShortcuts(BuildContext context) {
    // Add a focus node to the context to capture keyboard events
    FocusScope.of(context).requestFocus(FocusNode());

    // Set up keyboard shortcuts
    ServicesBinding.instance.keyboard.addHandler(_handleKeyPress);
  }

  /// Handle key press events
  bool _handleKeyPress(KeyEvent event) {
    // Update control key state
    if (event is KeyDownEvent) {
      if (event.logicalKey == LogicalKeyboardKey.controlLeft ||
          event.logicalKey == LogicalKeyboardKey.controlRight) {
        _isControlPressed = true;
      }

      // Check for M key when control is pressed
      if (_isControlPressed && event.logicalKey == LogicalKeyboardKey.keyM) {
        // Show cursor when system menu is triggered
        CursorManager.instance.showCursor();
        onShowMenu();
        return true; // Event handled
      }
    } else if (event is KeyUpEvent) {
      if (event.logicalKey == LogicalKeyboardKey.controlLeft ||
          event.logicalKey == LogicalKeyboardKey.controlRight) {
        _isControlPressed = false;
      }
    }

    return false; // Event not handled
  }

  /// Dispose the handler
  void dispose() {
    // Remove keyboard handler
    if (PlatformUtils.isDesktop) {
      ServicesBinding.instance.keyboard.removeHandler(_handleKeyPress);
    }
  }
}
