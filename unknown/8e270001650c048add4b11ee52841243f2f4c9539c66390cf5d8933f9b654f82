import 'dart:convert';

/// Model class representing a screen from the Supabase 'screens' table
class Screen {
  final String id; // Changed from int to String for UUID
  final String name;
  final String code;
  final bool isRegistered;
  final String? location;
  final String? description;
  final String? updateGuid; // Added updateGuid field
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic>? metadata;
  final String? startTime;
  final String? endTime;
  final DateTime? trialEndsAt;
  final String? subscriptionStatus;

  Screen({
    required this.id,
    required this.name,
    required this.code,
    required this.isRegistered,
    this.location,
    this.description,
    this.updateGuid,
    this.createdAt,
    this.updatedAt,
    this.metadata,
    this.startTime,
    this.endTime,
    this.trialEndsAt,
    this.subscriptionStatus,
  });

  /// Create a Screen from a JSON map
  factory Screen.fromJson(Map<String, dynamic> json) {
    return Screen(
      id: json['id']?.toString() ?? '',
      name: json['name'] ?? '',
      code: json['code']?.toString() ?? '', // Ensure code is a string
      isRegistered: json['is_registered'] ?? false,
      location: json['location'],
      description: json['description'],
      updateGuid: json['updateguid']?.toString(), // Added updateGuid field
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
      metadata: json['metadata'] != null
          ? (json['metadata'] is String
              ? jsonDecode(json['metadata'])
              : json['metadata'])
          : null,
      startTime: json['start_time']?.toString(),
      endTime: json['end_time']?.toString(),
      trialEndsAt: json['trial_ends_at'] != null
          ? DateTime.parse(json['trial_ends_at'])
          : null,
      subscriptionStatus: json['subscription_status']?.toString(),
    );
  }

  /// Convert Screen to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'code': code,
      'is_registered': isRegistered,
      'location': location,
      'description': description,
      'updateguid': updateGuid, // Added updateGuid field
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'metadata': metadata != null
          ? (metadata is String ? metadata : jsonEncode(metadata))
          : null,
      'start_time': startTime,
      'end_time': endTime,
      'trial_ends_at': trialEndsAt?.toIso8601String(),
      'subscription_status': subscriptionStatus,
    };
  }

  @override
  String toString() {
    return 'Screen(id: $id, name: $name, code: $code, isRegistered: $isRegistered)';
  }
}
