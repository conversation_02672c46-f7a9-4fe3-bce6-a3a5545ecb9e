import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:signage/ui/screens/player_screen.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  testWidgets('PlayerScreen shows loading indicator', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(
      const MaterialApp(
        home: PlayerScreen(),
      ),
    );

    // Verify that the loading indicator is shown
    expect(find.byType(CircularProgressIndicator), findsOneWidget);
  });
}
