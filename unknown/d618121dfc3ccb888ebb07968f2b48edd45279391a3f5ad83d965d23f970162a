import 'dart:async';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

/// Controller class for managing API data lifecycle and widget coordination
class ApiDataController extends ChangeNotifier {
  /// The API URL to fetch data from
  final String apiUrl;

  /// The API response data
  dynamic apiResponseData;

  /// The API data preview duration in seconds (0 means use natural video duration)
  final int apiDataPreviewDuration;

  /// Callback when all API content has finished playing
  final VoidCallback onComplete;

  /// Total number of records in the API response
  int totalRecords = 0;

  /// Current record index
  int _currentRecordIndex = 0;

  /// Timer for advancing to the next record
  Timer? _displayTimer;

  /// Timer for refreshing API data
  Timer? _refreshTimer;

  /// Map to track video widgets by their unique IDs
  final Map<String, bool> _videoWidgets = {};

  /// Map to track video completion status
  final Map<String, bool> _videoCompletionStatus = {};

  /// Flag to track if controller is disposed
  bool _isDisposed = false;

  /// Flag to track if data is loading
  bool _isLoading = false;

  /// Flag to track if data has been loaded at least once
  bool _isInitialized = false;

  /// Flag to track if there was an error loading data
  bool _hasError = false;

  /// Error message if there was an error loading data
  String _errorMessage = '';

  /// Flag to track if we're advancing to the next record
  bool _isAdvancing = false;

  /// Public getter for isDisposed
  bool get isDisposed => _isDisposed;

  /// Public getter for isLoading
  bool get isLoading => _isLoading;

  /// Public getter for isInitialized
  bool get isInitialized => _isInitialized;

  /// Public getter for hasError
  bool get hasError => _hasError;

  /// Public getter for errorMessage
  String get errorMessage => _errorMessage;

  /// Create an ApiDataController
  ApiDataController({
    required this.apiUrl,
    this.apiResponseData,
    required this.apiDataPreviewDuration,
    required this.onComplete,
    this.totalRecords = 0,
  }) {
    debugPrint('ApiDataController: Created with apiUrl: $apiUrl, apiDataPreviewDuration: $apiDataPreviewDuration');

    // Fetch API data immediately
    fetchApiData();

    // Set up a refresh timer if needed (e.g., every 5 minutes)
    _setupRefreshTimer();

    // NOTE: We do NOT start the display timer here
    // The timer will be started explicitly by calling startDisplayTimer()
    // after all APIWidgets have been initialized and registered their videos
  }

  /// Set up a timer to refresh the API data periodically
  void _setupRefreshTimer() {
    // Cancel any existing refresh timer
    _refreshTimer?.cancel();

    // Set up a new refresh timer (e.g., every 5 minutes)
    const refreshIntervalMinutes = 5;
    debugPrint('ApiDataController: Setting up refresh timer for every $refreshIntervalMinutes minutes');

    _refreshTimer = Timer.periodic(
      Duration(minutes: refreshIntervalMinutes),
      (_) => fetchApiData(isRefresh: true)
    );
  }

  /// Fetch data from the API endpoint
  Future<void> fetchApiData({bool isRefresh = false}) async {
    if (_isDisposed) return;

    // If we're already loading data, don't start another request
    if (_isLoading) {
      debugPrint('ApiDataController: Already loading data, skipping fetch request');
      return;
    }

    try {
      _isLoading = true;
      if (!isRefresh) {
        // Only notify listeners if this is not a background refresh
        notifyListeners();
      }

      debugPrint('ApiDataController: ${isRefresh ? "Refreshing" : "Fetching"} data from $apiUrl');

      // Make the HTTP request
      final response = await http.get(Uri.parse(apiUrl));

      if (_isDisposed) return; // Check if disposed during the HTTP request

      if (response.statusCode == 200) {
        // Parse the response
        final dynamic data = jsonDecode(response.body);

        // Log the API response for debugging
        debugPrint('ApiDataController: API Response received with status ${response.statusCode}');

        if (data == null) {
          _hasError = true;
          _errorMessage = 'API returned null data';
          _isLoading = false;
          notifyListeners();

          if (!isRefresh && !_isInitialized) {
            // Only call onComplete if this is the first load attempt
            onComplete();
          }
          return;
        }

        // Store the API response data
        apiResponseData = data;

        // Determine if it's a list or a single object
        if (data is List) {
          totalRecords = data.length;
        } else {
          totalRecords = 1;
        }

        debugPrint('ApiDataController: Received $totalRecords records from API');

        // Reset the current record index if this is a refresh and we're at the end
        if (isRefresh && _currentRecordIndex >= totalRecords) {
          _currentRecordIndex = 0;
        }

        _isInitialized = true;
        _hasError = false;
        _errorMessage = '';

        // We no longer automatically start the timer here
        // The SlideShowWidget will explicitly call startDisplayTimer()
        // after all APIWidgets have been initialized and registered their videos
        debugPrint('ApiDataController: API data fetched successfully, waiting for all APIWidgets to initialize');
      } else {
        debugPrint('ApiDataController: API request failed with status ${response.statusCode}');

        _hasError = true;
        _errorMessage = 'API request failed with status ${response.statusCode}';

        if (!isRefresh && !_isInitialized) {
          // Only call onComplete if this is the first load attempt
          onComplete();
        }
      }
    } catch (e) {
      if (_isDisposed) return;

      debugPrint('ApiDataController: Error fetching API data: $e');

      _hasError = true;
      _errorMessage = 'Error fetching API data: $e';

      if (!isRefresh && !_isInitialized) {
        // Only call onComplete if this is the first load attempt
        onComplete();
      }
    } finally {
      if (!_isDisposed) {
        _isLoading = false;
        notifyListeners();
      }
    }
  }

  /// Get the current record data
  dynamic getCurrentRecordData() {
    if (apiResponseData == null) return null;

    return apiResponseData is List
        ? (_currentRecordIndex < totalRecords ? apiResponseData[_currentRecordIndex] : null)
        : apiResponseData;
  }

  /// Get the current record index
  int get currentRecordIndex => _currentRecordIndex;

  /// Update the API response data and total records
  void updateApiData(dynamic data, int totalRecords) {
    if (_isDisposed) return;

    apiResponseData = data;
    this.totalRecords = totalRecords;
    _isInitialized = true;
    debugPrint('ApiDataController: Updated API data with $totalRecords records');
    notifyListeners();
  }

  /// Register a video widget with the controller
  /// This is called by APIWidget when a video is created
  void registerVideoWidget(String videoId) {
    if (isDisposed) {
      debugPrint('ApiDataController: Cannot register video $videoId - controller is disposed');
      return;
    }

    // Check if this video is already registered
    if (_videoWidgets.containsKey(videoId)) {
      debugPrint('ApiDataController: Video $videoId is already registered, resetting completion status');
      _videoCompletionStatus[videoId] = false;
      return;
    }

    // Register the video
    _videoWidgets[videoId] = true;
    _videoCompletionStatus[videoId] = false;

    // Log registration and current state
    debugPrint('ApiDataController: Registered video widget $videoId (total: ${_videoWidgets.length})');

    // If this is the first video registered, we need to cancel any existing timer
    // and start a new one that accounts for videos
    if (_videoWidgets.length == 1) {
      debugPrint('ApiDataController: First video registered, updating timer strategy');
      startDisplayTimer();
    }
  }

  /// Unregister a video widget from the controller
  /// This is called by APIWidget when a video is disposed
  void unregisterVideoWidget(String videoId) {
    if (isDisposed) {
      debugPrint('ApiDataController: Cannot unregister video $videoId - controller is disposed');
      return;
    }

    // Check if this video is registered
    if (!_videoWidgets.containsKey(videoId)) {
      debugPrint('ApiDataController: WARNING - Trying to unregister unknown video $videoId');
      return;
    }

    // Remove the video from tracking
    _videoWidgets.remove(videoId);
    _videoCompletionStatus.remove(videoId);

    // Log unregistration and current state
    debugPrint('ApiDataController: Unregistered video widget $videoId (remaining: ${_videoWidgets.length})');

    // If there are no more videos, we need to update the timer strategy
    if (_videoWidgets.isEmpty) {
      debugPrint('ApiDataController: No more videos registered, updating timer strategy');
      startDisplayTimer();
    }
    // If all remaining videos are completed, we should advance to the next record
    else if (_videoWidgets.keys.every((id) => _videoCompletionStatus[id] == true)) {
      debugPrint('ApiDataController: All remaining videos are completed after unregistering $videoId, advancing to next record');
      advanceToNextRecord();
    }
  }

  /// Notify the controller that a video has completed
  /// This is called by APIWidget when a video completes playback
  /// All APIWidgets will be synchronized by this method
  void notifyVideoComplete(String videoId) {
    if (isDisposed || _isAdvancing) {
      debugPrint('ApiDataController: Ignoring video completion for $videoId - controller is ${isDisposed ? "disposed" : "advancing"}');
      return;
    }

    // Check if this video is registered
    if (!_videoWidgets.containsKey(videoId)) {
      debugPrint('ApiDataController: WARNING - Received completion notification for unregistered video $videoId');
      return;
    }

    debugPrint('ApiDataController: Video $videoId completed');

    // Mark this video as completed
    _videoCompletionStatus[videoId] = true;

    // Log the current completion status
    final completedCount = _videoCompletionStatus.values.where((v) => v).length;
    final totalCount = _videoWidgets.length;
    debugPrint('ApiDataController: Video completion status: $completedCount/$totalCount videos completed');

    // Log all video IDs and their completion status
    for (final id in _videoWidgets.keys) {
      final isCompleted = _videoCompletionStatus[id] ?? false;
      debugPrint('ApiDataController: Video $id - ${isCompleted ? "COMPLETED" : "not completed"}');
    }

    // Check if all videos have completed
    final allVideosCompleted = _videoWidgets.keys.every(
      (id) => _videoCompletionStatus[id] == true
    );

    // Only proceed if we have videos and all have completed
    if (allVideosCompleted && _videoWidgets.isNotEmpty) {
      // Cancel any existing timer since videos have completed
      cancelTimer();

      // Log the advancement reason
      if (apiDataPreviewDuration == 0) {
        debugPrint('ApiDataController: All videos completed with apiDataPreviewDuration=0, advancing to next record/schedule item');
      } else {
        debugPrint('ApiDataController: All videos completed with apiDataPreviewDuration=$apiDataPreviewDuration, advancing to next API record');
      }

      // Add a small delay before advancing to ensure all videos have fully completed
      // This helps prevent race conditions where one video might still be in the process of completing
      Future.delayed(const Duration(milliseconds: 100), () {
        if (!isDisposed && !_isAdvancing) {
          // Advance to the next record
          // This will notify all APIWidgets through the ChangeNotifier mechanism
          advanceToNextRecord();
        }
      });
    } else {
      // Not all videos have completed yet
      debugPrint('ApiDataController: Waiting for other videos to complete - completed: $completedCount/$totalCount');
    }
  }

  /// Start the display timer for the current record
  /// This is the ONLY method that should start a timer for all APIWidgets in a slide
  /// All APIWidgets will be synchronized by this single global timer
  void startDisplayTimer() {
    if (isDisposed) return;

    // Cancel any existing timer
    cancelTimer();

    // Log the current state for debugging
    debugPrint('ApiDataController: Starting GLOBAL display timer - videoWidgets: ${_videoWidgets.length}, apiDataPreviewDuration: $apiDataPreviewDuration');

    // Determine if we need to start a timer based on the presence of videos and apiDataPreviewDuration
    if (_videoWidgets.isEmpty) {
      // CASE 1 & 3: No videos registered
      final durationInSeconds = apiDataPreviewDuration > 0
          ? apiDataPreviewDuration  // CASE 3: api_data_preview_duration > 0, no videos
          : 15;                     // CASE 1: api_data_preview_duration = 0, no videos (use 15 seconds)

      debugPrint('ApiDataController: Starting GLOBAL display timer for $durationInSeconds seconds (no videos)');

      _displayTimer = Timer(Duration(seconds: durationInSeconds), () {
        debugPrint('ApiDataController: GLOBAL timer expired (no videos), advancing to next record');
        advanceToNextRecord();
      });
    } else {
      // We have videos registered
      if (apiDataPreviewDuration > 0) {
        // CASE 4: api_data_preview_duration > 0, with videos
        // Start a safety timer that will advance after apiDataPreviewDuration
        // This ensures we don't get stuck if a video fails to complete
        debugPrint('ApiDataController: Starting GLOBAL SAFETY timer for $apiDataPreviewDuration seconds (with videos)');

        _displayTimer = Timer(Duration(seconds: apiDataPreviewDuration), () {
          debugPrint('ApiDataController: GLOBAL SAFETY timer expired (with videos), advancing to next record');
          advanceToNextRecord();
        });
      } else {
        // CASE 2: api_data_preview_duration = 0, with videos
        // Start a long safety timer to ensure we don't get stuck if videos fail to complete
        debugPrint('ApiDataController: Starting GLOBAL SAFETY timer for 60 seconds (with videos, natural duration)');

        _displayTimer = Timer(Duration(seconds: 60), () {
          debugPrint('ApiDataController: GLOBAL SAFETY timer expired (with videos, natural duration), advancing to next record');
          advanceToNextRecord();
        });

        // We'll still rely on video completion events for normal advancement
        // The notifyVideoComplete method will handle advancement when all videos complete
        // This safety timer is just a fallback
      }
    }
  }

  /// Cancel the active timer if any
  void cancelTimer() {
    if (_displayTimer != null && _displayTimer!.isActive) {
      _displayTimer!.cancel();
      _displayTimer = null;
      debugPrint('ApiDataController: Cancelled active timer');
    }
  }

  /// Advance to the next record or complete if all records have been displayed
  /// This is the central method that controls record advancement for all APIWidgets
  /// All APIWidgets will be synchronized by this method through the ChangeNotifier mechanism
  void advanceToNextRecord() {
    if (isDisposed || _isAdvancing) return;

    _isAdvancing = true;

    // Cancel any active timer
    cancelTimer();

    // Increment the record index
    final nextIndex = _currentRecordIndex + 1;

    if (nextIndex < totalRecords) {
      // Move to the next record
      _currentRecordIndex = nextIndex;
      debugPrint('ApiDataController: GLOBAL advancement to record $_currentRecordIndex of $totalRecords');

      // Reset advancing flag
      _isAdvancing = false;

      // Notify listeners that we've moved to the next record
      // This will also reset video tracking and start a new timer if needed
      // All APIWidgets will be notified through the ChangeNotifier mechanism
      notifyRecordChanged();
    } else {
      // CASE 5: Last record reached
      // All records have been displayed, dispose slide and advance to next schedule item
      debugPrint('ApiDataController: All records displayed (${_currentRecordIndex + 1}/$totalRecords), disposing slide and advancing to next schedule item');

      // Reset advancing flag
      _isAdvancing = false;

      // Complete and move to next scheduleItem
      // This will trigger the SlideShowWidget to move to the next scheduleItem
      onComplete();
    }
  }

  /// Notify that the record has changed
  /// This method is called when the current record index changes
  /// It notifies all APIWidgets through the ChangeNotifier mechanism
  /// This ensures all APIWidgets display the same record at the same time
  void notifyRecordChanged() {
    debugPrint('ApiDataController: GLOBAL record change to $_currentRecordIndex of $totalRecords');

    // Reset video tracking for the new record
    _videoWidgets.clear();
    _videoCompletionStatus.clear();

    // Start a new timer for the next record
    // This is important for both cases (apiDataPreviewDuration > 0 and apiDataPreviewDuration == 0)
    // to ensure consistent timing across all APIWidgets
    startDisplayTimer();

    // Notify all listeners (APIWidgets) about the record change
    // This will trigger _onControllerUpdate in all APIWidgets
    // Which will then call _syncWithController to update their state
    notifyListeners();
  }

  /// Reset the controller to the first record
  void resetToFirstRecord() {
    if (isDisposed) return;

    // Cancel any active timer
    cancelTimer();

    // Reset to the first record
    _currentRecordIndex = 0;

    // Reset video tracking
    _videoWidgets.clear();
    _videoCompletionStatus.clear();

    // Start the display timer for the first record
    startDisplayTimer();

    // Notify listeners
    notifyListeners();
  }

  /// Check if all APIWidgets have been initialized and registered their videos
  /// This should be called by the SlideShowWidget after all APIWidgets have been created
  bool areAllWidgetsReady() {
    if (isDisposed) return false;

    // We consider all widgets ready when:
    // 1. The API data has been fetched successfully
    // 2. We have at least one record
    bool dataReady = _isInitialized && totalRecords > 0;

    debugPrint('ApiDataController: Checking if all widgets are ready - dataReady: $dataReady, videoWidgets: ${_videoWidgets.length}');

    return dataReady;
  }

  /// Start the global timer for all APIWidgets
  /// This should be called by the SlideShowWidget after all APIWidgets have been created
  void startGlobalTimer() {
    if (isDisposed) return;

    debugPrint('ApiDataController: Starting global timer for all APIWidgets');

    // Reset to the first record
    _currentRecordIndex = 0;

    // Reset video tracking
    _videoCompletionStatus.clear();
    // Note: We don't clear _videoWidgets here because we want to keep track of registered videos

    // Start the display timer
    startDisplayTimer();

    // Notify listeners to ensure all APIWidgets update
    notifyListeners();
  }

  /// Dispose the controller
  @override
  void dispose() {
    if (_isDisposed) return;

    debugPrint('ApiDataController: Disposing');

    // Cancel any active timers
    cancelTimer();
    _refreshTimer?.cancel();

    // Clear all maps
    _videoWidgets.clear();
    _videoCompletionStatus.clear();

    _isDisposed = true;

    super.dispose();
  }
}
