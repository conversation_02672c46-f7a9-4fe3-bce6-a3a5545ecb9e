import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:signage/core/models/settings.dart';
import 'package:signage/core/services/player_schedule_service.dart';
import 'package:signage/utils/time_utils.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  group('TimeUtils', () {
    test('parseTimeString should parse valid time strings', () {
      final time1 = TimeUtils.parseTimeString('09:30:00');
      expect(time1, isNotNull);
      expect(time1!.hour, 9);
      expect(time1.minute, 30);
      expect(time1.second, 0);

      final time2 = TimeUtils.parseTimeString('23:59:59');
      expect(time2, isNotNull);
      expect(time2!.hour, 23);
      expect(time2.minute, 59);
      expect(time2.second, 59);
    });

    test('parseTimeString should return null for invalid time strings', () {
      expect(TimeUtils.parseTimeString('25:00:00'), isNull);
      expect(TimeUtils.parseTimeString('12:60:00'), isNull);
      expect(TimeUtils.parseTimeString('12:30:60'), isNull);
      expect(TimeUtils.parseTimeString('invalid'), isNull);
      expect(TimeUtils.parseTimeString('12:30'), isNull);
      expect(TimeUtils.parseTimeString(''), isNull);
    });

    test('isCurrentTimeBetween should work correctly', () {
      final now = DateTime.now();
      final startTime = DateTime(now.year, now.month, now.day, 9, 0, 0);
      final endTime = DateTime(now.year, now.month, now.day, 17, 0, 0);

      // Mock current time to be between start and end
      final mockCurrentTime = DateTime(now.year, now.month, now.day, 12, 0, 0);

      // Note: This test would need to mock the current time to be reliable
      // For now, we'll just test the logic structure
      expect(mockCurrentTime.isAfter(startTime), isTrue);
      expect(mockCurrentTime.isBefore(endTime), isTrue);
    });

    test('calculateDurationUntil should calculate correct duration', () {
      final now = DateTime.now();
      final futureTime = now.add(const Duration(hours: 2));

      final duration = TimeUtils.calculateDurationUntil(futureTime);
      expect(duration.inHours, 2);
    });

    test('formatTimeString should format time correctly', () {
      final time = DateTime(2024, 1, 1, 9, 5, 3);
      final formatted = TimeUtils.formatTimeString(time);
      expect(formatted, '09:05:03');
    });
  });

  group('PlayerScheduleService', () {
    late PlayerScheduleService service;
    bool contentStarted = false;
    bool contentStopped = false;

    setUp(() {
      contentStarted = false;
      contentStopped = false;

      service = PlayerScheduleService(
        onStartContent: () => contentStarted = true,
        onStopContent: () => contentStopped = true,
      );
    });

    tearDown(() {
      service.dispose();
    });

    test('should initialize with null settings and start content', () async {
      await service.initialize(null);
      service.start();

      expect(service.isContentDisplaying, isTrue);
      expect(contentStarted, isTrue);
    });

    test('should initialize with settings without start/end times', () async {
      final settings = Settings(
        screenId: 'test-id',
        screenName: 'Test Screen',
        code: 'TEST001',
        startTime: null,
        endTime: null,
      );

      await service.initialize(settings);
      service.start();

      expect(service.isContentDisplaying, isTrue);
      expect(contentStarted, isTrue);
    });

    test('should initialize with invalid time format', () async {
      final settings = Settings(
        screenId: 'test-id',
        screenName: 'Test Screen',
        code: 'TEST001',
        startTime: 'invalid-time',
        endTime: '25:00:00',
      );

      await service.initialize(settings);
      service.start();

      expect(service.isContentDisplaying, isTrue);
      expect(contentStarted, isTrue);
    });

    test('should handle valid time settings', () async {
      final settings = Settings(
        screenId: 'test-id',
        screenName: 'Test Screen',
        code: 'TEST001',
        startTime: '09:00:00',
        endTime: '17:00:00',
      );

      await service.initialize(settings);
      service.start();

      // The behavior depends on current time, so we just verify no errors occur
      expect(service.isContentDisplaying, isA<bool>());
    });

    test('should stop and start correctly', () {
      service.start();
      expect(service.isContentDisplaying, isFalse); // No initialization yet

      service.stop();
      expect(service.isContentDisplaying, isFalse);
    });
  });
}
