import 'dart:io';

/// Utility class for network-related operations
class NetworkUtils {
  /// Check if the device has internet connectivity
  static Future<bool> hasInternetConnection() async {
    try {
      // Try to connect to a reliable host
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    } catch (e) {
      print('Error checking internet connection: $e');
      return false;
    }
  }
}
