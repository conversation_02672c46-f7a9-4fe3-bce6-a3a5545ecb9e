import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:signage/core/controllers/system_menu_controller.dart';
import 'package:signage/ui/screens/splash_screen.dart';
import 'package:signage/ui/widgets/system_menu_widget.dart';

class SignageApp extends StatefulWidget {
  const SignageApp({super.key});

  @override
  State<SignageApp> createState() => _SignageAppState();
}

class _SignageAppState extends State<SignageApp> {
  // System menu controller
  final SystemMenuController _systemMenuController = SystemMenuController();

  // State for system menu
  bool _showSystemMenu = false;

  @override
  void dispose() {
    // Dispose system menu controller
    _systemMenuController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Set system UI to be fullscreen without title bar
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);

    return MaterialApp(
      title: 'Signage Player',
      debugShowCheckedModeBanner: false,
      navigatorKey: SystemMenuController.navigatorKey,
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      builder: (context, child) {
        // Initialize system menu controller
        _systemMenuController.initialize(
          context,
          onShowMenu: _toggleSystemMenu,
        );

        return Material(
          type: MaterialType.transparency,
          child: Stack(
            children: [
              // Main app content
              child!,

              // System menu widget
              SystemMenuWidget(
                isMenuVisible: _showSystemMenu,
                onMenuVisibilityChanged: (isVisible) {
                  setState(() {
                    _showSystemMenu = isVisible;
                  });
                },
                onRestartPlayer: () => _systemMenuController.restartPlayer(context),
              ),
            ],
          ),
        );
      },
      home: const SplashScreen(),
    );
  }

  /// Toggle system menu visibility
  void _toggleSystemMenu() {
    setState(() {
      _showSystemMenu = !_showSystemMenu;
    });
  }
}
