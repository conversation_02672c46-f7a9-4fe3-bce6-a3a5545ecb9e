import 'dart:async';
import 'package:flutter/material.dart';
import 'package:signage/core/services/cursor_manager.dart';
import 'package:signage/utils/platform_utils.dart';

/// A widget that controls cursor visibility based on the CursorManager state
class CursorControlledWidget extends StatefulWidget {
  final Widget child;

  const CursorControlledWidget({
    super.key,
    required this.child,
  });

  @override
  State<CursorControlledWidget> createState() => _CursorControlledWidgetState();
}

class _CursorControlledWidgetState extends State<CursorControlledWidget> {
  Timer? _refreshTimer;

  @override
  void initState() {
    super.initState();

    // Set up a timer to periodically check cursor state changes
    // This is a simple approach since we don't have a stream-based cursor manager
    if (PlatformUtils.isDesktop) {
      _refreshTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
        if (mounted) {
          setState(() {
            // This will trigger a rebuild to check the current cursor state
          });
        }
      });
    }
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Only apply cursor control on desktop platforms
    if (!PlatformUtils.isDesktop) {
      return widget.child;
    }

    // Get the current cursor visibility state
    final isCursorVisible = CursorManager.instance.isCursorVisible;

    return MouseRegion(
      cursor: isCursorVisible ? SystemMouseCursors.basic : SystemMouseCursors.none,
      child: widget.child,
    );
  }
}
