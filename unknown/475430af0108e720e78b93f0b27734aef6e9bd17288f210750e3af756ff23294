import 'dart:convert';

/// Model class representing a media file from the Supabase 'media_files' table
class Media {
  final String id; // Changed from int to String for UUID
  final String name;
  final String fileUrl;
  final String? fileType; // 'image' or 'video', can be null
  final int? duration; // in seconds, for videos
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic>? metadata;

  Media({
    required this.id,
    required this.name,
    required this.fileUrl,
    this.fileType,
    this.duration,
    this.createdAt,
    this.updatedAt,
    this.metadata,
  });

  /// Create a Media from a JSON map
  factory Media.fromJson(Map<String, dynamic> json) {
    return Media(
      id: json['id']?.toString() ?? '', // Ensure id is a string
      name: json['name'] ?? '',
      fileUrl: json['file_url'] ?? '',
      fileType: json['file_type']?.toString(), // Can be null
      duration: json['duration'] != null ? int.tryParse(json['duration'].toString()) : null,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
      metadata: json['metadata'] != null
          ? (json['metadata'] is String
              ? jsonDecode(json['metadata'])
              : json['metadata'])
          : null,
    );
  }

  /// Convert Media to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'file_url': fileUrl,
      'file_type': fileType,
      'duration': duration,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'metadata': metadata != null
          ? (metadata is String ? metadata : jsonEncode(metadata))
          : null,
    };
  }

  /// Extract the file path from the Supabase storage URL
  String get storagePath {
    // Example URL: https://oatyudgnhndxrwwlbxsj.supabase.co/storage/v1/object/public/medialibrary/1c3a76e6d-2f37-4b41-b5bc-acc9f244b439/promo1.mp4
    final uri = Uri.parse(fileUrl);
    final pathSegments = uri.pathSegments;

    // Find the index of 'medialibrary' in the path segments
    final medialibraryIndex = pathSegments.indexOf('medialibrary');

    if (medialibraryIndex >= 0 && medialibraryIndex < pathSegments.length - 1) {
      // Join all segments after 'medialibrary'
      return pathSegments.sublist(medialibraryIndex + 1).join('/');
    }

    // Fallback: use the filename as the path
    return name;
  }

  /// Get the local file name for this media
  String get localFileName {
    // Use the last part of the storage path as the file name
    final parts = storagePath.split('/');
    return parts.last;
  }

  @override
  String toString() {
    return 'Media(id: $id, name: $name, fileType: $fileType)';
  }
}
