# Phase 9: Player On-Off Feature Implementation

## Overview

Phase 9 implements a player scheduling feature that allows the digital signage application to automatically start and stop content display based on configured start and end times. This feature includes screen wake/lock management and proper timer handling.

## Features Implemented

### 1. Time-Based Content Control
- **Start Time**: Content begins displaying at the specified time
- **End Time**: Content stops displaying and shows a black screen
- **Format**: Times are specified in "HH:mm:ss" format (24-hour)
- **Validation**: Invalid time formats are handled gracefully

### 2. Timer Management
- **Start Timer**: Calculates time until content should start
- **Stop Timer**: Calculates time until content should stop
- **Next Day Timer**: Handles transitions across midnight
- **Automatic Cycling**: Seamlessly transitions between start/stop states

### 3. Screen Management
- **Wake Lock**: Keeps screen alive during content display
- **Screen Wake**: Wakes screen when content starts
- **Screen Lock**: Allows screen to sleep during black screen periods
- **Cross-Platform**: Works on Android, Windows, and Linux

### 4. Black Screen Display
- **Professional Appearance**: Shows solid black screen during off hours
- **No Loading Artifacts**: Transparent loading indicators
- **Immediate Response**: Instant transitions between states

## Files Created/Modified

### New Files
1. **`lib/core/services/player_schedule_service.dart`**
   - Main service handling schedule logic
   - Timer management and state transitions
   - Wake lock integration

2. **`lib/utils/time_utils.dart`**
   - Time parsing and validation utilities
   - Duration calculations
   - Time comparison functions

3. **`test/player_schedule_service_test.dart`**
   - Comprehensive test suite
   - Time utility tests
   - Service behavior validation

### Modified Files
1. **`lib/ui/screens/player_screen.dart`**
   - Integrated PlayerScheduleService
   - Added black screen display logic
   - Callback handlers for start/stop events

2. **`pubspec.yaml`**
   - Added `wakelock_plus: ^1.2.8` dependency
   - Updated `package_info_plus` to resolve conflicts

## Configuration

### Settings Format
The feature is configured through the `settings.json` file:

```json
{
  "screenId": "your-screen-id",
  "screenName": "Your Screen Name",
  "code": "SCREEN001",
  "startTime": "09:00:00",
  "endTime": "17:00:00"
}
```

### Behavior Scenarios

#### 1. Normal Operation (startTime and endTime provided)
- **Between start and end time**: Display content normally
- **Before start time**: Show black screen, timer set to start time
- **After end time**: Show black screen, timer set to next day's start time

#### 2. Fallback Scenarios
- **No startTime/endTime**: Content plays continuously
- **Invalid time format**: Content plays continuously with warning
- **Null settings**: Content plays continuously

#### 3. Timer Logic
- **Current time between start-end**: Start content, set stop timer
- **Current time before start**: Show black screen, set start timer
- **Current time after end**: Show black screen, set next-day start timer

## Usage Examples

### Example 1: Business Hours (9 AM - 5 PM)
```json
{
  "startTime": "09:00:00",
  "endTime": "17:00:00"
}
```
- Content displays from 9:00 AM to 5:00 PM
- Black screen from 5:00 PM to 9:00 AM next day

### Example 2: Extended Hours (6 AM - 10 PM)
```json
{
  "startTime": "06:00:00",
  "endTime": "22:00:00"
}
```
- Content displays from 6:00 AM to 10:00 PM
- Black screen from 10:00 PM to 6:00 AM next day

### Example 3: Overnight Operation (10 PM - 6 AM)
```json
{
  "startTime": "22:00:00",
  "endTime": "06:00:00"
}
```
- Content displays from 10:00 PM to 6:00 AM next day
- Black screen from 6:00 AM to 10:00 PM

## Technical Implementation

### Service Architecture
```
PlayerScreen
    ├── PlayerScheduleService
    │   ├── TimeUtils (parsing/validation)
    │   ├── Timer Management
    │   └── WakelockPlus (screen control)
    └── Content Display Logic
```

### State Management
- `_shouldDisplayContent`: Boolean flag controlling display state
- `_isContentDisplaying`: Service state tracking
- Callback-based communication between service and UI

### Error Handling
- Graceful fallback for invalid time formats
- Exception handling for wake lock operations
- Comprehensive logging for debugging

## Testing

### Running Tests
```bash
flutter test test/player_schedule_service_test.dart
```

### Test Coverage
- Time parsing validation
- Duration calculations
- Service initialization scenarios
- State management verification

### Manual Testing
1. Set different start/end times in settings.json
2. Observe content display behavior
3. Verify black screen during off hours
4. Check timer accuracy and transitions

## Dependencies

### Added Dependencies
- **wakelock_plus**: Screen wake lock management
- **package_info_plus**: Updated for compatibility

### Platform Support
- **Android**: Full support with wake lock
- **Windows**: Full support with wake lock
- **Linux**: Full support with wake lock
- **iOS**: Compatible (not tested)

## Logging

The implementation includes comprehensive logging:
- Service start/stop events
- Timer creation and expiration
- State transitions
- Error conditions
- Time calculations

Example log output:
```
PlayerScheduleService: Parsed start time: 09:00:00
PlayerScheduleService: Parsed end time: 17:00:00
PlayerScheduleService: Current time is between start and end time - starting content display
PlayerScheduleService: Setting stop timer for 28800 seconds (8h 0m 0s)
```

## Future Enhancements

Potential improvements for future phases:
1. **Weekly Schedules**: Different times for different days
2. **Holiday Support**: Special schedules for holidays
3. **Remote Control**: Dynamic schedule updates via API
4. **Multiple Time Slots**: Multiple on/off periods per day
5. **Gradual Transitions**: Fade in/out effects
6. **Energy Monitoring**: Power consumption tracking

## Bug Fixes

### Issue: Content Continues Playing After End Time
**Problem**: When the app started after the configured end time, it would show a black screen briefly but then continue playing content instead of maintaining the black screen.

**Root Cause**: The initialization sequence had a race condition:
1. PlayerScheduleService correctly determined it should show black screen
2. But `_startPlayback()` was called immediately after initialization
3. The schedule state check wasn't implemented in the playback logic

**Solution**:
1. Added schedule state check in `_startPlayback()` method
2. Modified `_stopContentDisplay()` to always call the callback
3. Enhanced `_handleStopContent()` to immediately clear content widgets
4. Added schedule checks in `_onContentComplete()` to prevent transitions

**Code Changes**:
- `PlayerScreen._startPlayback()`: Added `_shouldDisplayContent` check
- `PlayerScreen._onContentComplete()`: Added schedule state validation
- `PlayerScreen._handleStopContent()`: Clear content widgets immediately
- `PlayerScheduleService._stopContentDisplay()`: Always call callback

## Troubleshooting

### Common Issues
1. **Content not stopping**: Check time format and system clock
2. **Black screen not showing**: Verify `_shouldDisplayContent` state
3. **Wake lock errors**: Normal in test environment
4. **Timer not firing**: Check for timezone issues
5. **Content continues after end time**: Ensure latest fix is applied

### Debug Steps
1. Enable debug logging
2. Verify settings.json format
3. Check system time accuracy
4. Monitor service state changes
5. Validate timer calculations
6. Check for "Content should not be displayed according to schedule" logs
