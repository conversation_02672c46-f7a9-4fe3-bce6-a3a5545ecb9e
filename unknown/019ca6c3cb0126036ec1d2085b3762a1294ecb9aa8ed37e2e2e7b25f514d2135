import 'dart:io';
import 'package:flutter/material.dart';

/// A widget for displaying images in the player
class ImageWidget extends StatelessWidget {
  /// The path to the image file
  final String filePath;

  /// The width of the widget
  final double? width;

  /// The height of the widget
  final double? height;

  /// Callback when the image is loaded
  final VoidCallback? onLoaded;

  /// Callback when there's an error loading the image
  final Function(Object)? onError;

  /// Creates an ImageWidget
  const ImageWidget({
    super.key,
    required this.filePath,
    this.width,
    this.height,
    this.onLoaded,
    this.onError,
  });

  @override
  Widget build(BuildContext context) {
    // Pre-load the image to improve performance
    final imageProvider = FileImage(File(filePath));

    // Precache the image to speed up rendering
    precacheImage(imageProvider, context);

    return Image(
      image: imageProvider,
      width: width,
      height: height,
      fit: BoxFit.fill, // Fill the entire widget without maintaining aspect ratio
      errorBuilder: (context, error, stackTrace) {
        // Call the error callback immediately if provided
        if (onError != null) {
          onError!(error);
        }

        // Show a simple black container on error to avoid visual artifacts
        return Container(
          width: width,
          height: height,
          color: Colors.black,
        );
      },
      frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
        // Call the loaded callback immediately when the image is loaded
        if (frame != null && onLoaded != null && !wasSynchronouslyLoaded) {
          // Call directly without post-frame callback to avoid delays
          onLoaded!();
        } else if (wasSynchronouslyLoaded && onLoaded != null) {
          // If image was loaded synchronously, call the callback immediately
          onLoaded!();
        }

        // Always return the child directly
        return child;
      },
    );
  }
}
