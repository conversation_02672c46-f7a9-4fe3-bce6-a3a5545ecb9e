import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:signage/core/models/proof_of_play.dart';
import 'package:signage/core/models/screen_activity.dart';
import 'package:signage/core/models/settings.dart';
import 'package:signage/core/services/supabase_service.dart';

/// Service for handling activity and proof of play logging
class LoggingService {
  /// Singleton instance
  static final LoggingService _instance = LoggingService._internal();

  /// Factory constructor
  factory LoggingService() => _instance;

  /// Internal constructor
  LoggingService._internal();

  /// Whether the service is running
  bool _isRunning = false;

  /// Screen ID from settings
  String? _screenId;

  /// Queue of pending logs to be sent to Supabase
  final List<ScreenActivity> _activityQueue = [];
  final List<ProofOfPlay> _proofOfPlayQueue = [];

  /// Timer for processing the queue
  Timer? _queueProcessingTimer;

  /// Start the logging service
  Future<void> start() async {
    if (_isRunning) {
      debugPrint('LoggingService: Already running');
      return;
    }

    debugPrint('LoggingService: Starting');
    _isRunning = true;

    // Load settings to get screen ID
    await _loadSettings();

    // Log the screen ID for debugging
    debugPrint('LoggingService: Loaded screen ID: $_screenId');

    // Log player start
    if (_screenId != null) {
      debugPrint('LoggingService: Logging player start');
      logPlayerStart();
    } else {
      debugPrint('LoggingService: WARNING - Cannot log player start, screen ID is null');
    }

    // Start queue processing timer
    _startQueueProcessing();
  }

  /// Stop the logging service
  void stop() {
    if (!_isRunning) {
      debugPrint('LoggingService: Not running');
      return;
    }

    debugPrint('LoggingService: Stopping');
    _isRunning = false;

    // Cancel queue processing timer
    _queueProcessingTimer?.cancel();
    _queueProcessingTimer = null;

    // Process any remaining logs in the queue
    _processQueue();
  }

  /// Load settings from storage
  Future<void> _loadSettings() async {
    final settings = await Settings.load();
    if (settings != null) {
      _screenId = settings.screenId;
      debugPrint('LoggingService: Loaded settings for screen $_screenId');
    } else {
      debugPrint('LoggingService: Failed to load settings');
    }
  }

  /// Start the queue processing timer
  void _startQueueProcessing() {
    // Process queue every 10 seconds (reduced from 30 seconds for faster feedback)
    _queueProcessingTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      _processQueue();
    });

    // Process queue immediately on start
    _processQueue();
  }

  // Flag to prevent multiple queue processing at the same time
  bool _isProcessingQueue = false;

  /// Process the queue of pending logs
  Future<void> _processQueue() async {
    if (_screenId == null) {
      debugPrint('LoggingService: Screen ID not available, cannot process queue');
      return;
    }

    // Prevent multiple simultaneous queue processing
    if (_isProcessingQueue) {
      debugPrint('LoggingService: Queue is already being processed, skipping');
      return;
    }

    _isProcessingQueue = true;

    try {
      debugPrint('LoggingService: Processing queue - Activities: ${_activityQueue.length}, Proof of Play: ${_proofOfPlayQueue.length}');

      // Process activity logs
      if (_activityQueue.isNotEmpty) {
        // Create a copy of the queue and clear it to prevent duplicate processing
        final activitiesToProcess = List<ScreenActivity>.from(_activityQueue);
        _activityQueue.clear();

        debugPrint('LoggingService: Processing ${activitiesToProcess.length} activity logs');

        // Group file download logs by file name to detect duplicates
        final Map<String, List<ScreenActivity>> fileDownloadLogs = {};

        for (final activity in activitiesToProcess) {
          // Check if this is a file download log
          if (activity.fileDownloadCount != null && activity.totalFileDownload != null) {
            // Extract file name from log details (format: "Downloaded file: filename.ext (size MB) - count/total")
            final match = RegExp(r'Downloaded file: (.*?) \(').firstMatch(activity.logDetails);
            if (match != null && match.groupCount >= 1) {
              final fileName = match.group(1)!;

              if (!fileDownloadLogs.containsKey(fileName)) {
                fileDownloadLogs[fileName] = [];
              }

              fileDownloadLogs[fileName]!.add(activity);
              continue; // Skip processing for now, we'll handle these separately
            }
          }

          // Process non-file download logs normally
          try {
            debugPrint('LoggingService: Sending activity log to Supabase: ${activity.logDetails}');
            final result = await SupabaseService.logScreenActivity(activity);
            debugPrint('LoggingService: Activity log result: $result');
          } catch (e) {
            debugPrint('LoggingService: Error logging activity: $e');
            // Add back to queue for retry
            _activityQueue.add(activity);
          }
        }

        // Now process file download logs, keeping only the latest one for each file
        for (final fileName in fileDownloadLogs.keys) {
          final logs = fileDownloadLogs[fileName]!;

          // Sort by fileDownloadCount to get the latest one
          logs.sort((a, b) => (b.fileDownloadCount ?? 0).compareTo(a.fileDownloadCount ?? 0));

          // Only process the latest log for each file
          final latestLog = logs.first;

          try {
            debugPrint('LoggingService: Sending latest file download log for $fileName: ${latestLog.logDetails}');
            final result = await SupabaseService.logScreenActivity(latestLog);
            debugPrint('LoggingService: File download log result: $result');
          } catch (e) {
            debugPrint('LoggingService: Error logging file download: $e');
            // Add back to queue for retry
            _activityQueue.add(latestLog);
          }

          // Log how many duplicate logs were skipped
          if (logs.length > 1) {
            debugPrint('LoggingService: Skipped ${logs.length - 1} duplicate download logs for $fileName');
          }
        }
      }

      // Process proof of play logs
      if (_proofOfPlayQueue.isNotEmpty) {
        final proofOfPlaysToProcess = List<ProofOfPlay>.from(_proofOfPlayQueue);
        _proofOfPlayQueue.clear();

        debugPrint('LoggingService: Processing ${proofOfPlaysToProcess.length} proof of play logs');

        for (final proofOfPlay in proofOfPlaysToProcess) {
          try {
            debugPrint('LoggingService: Sending proof of play log to Supabase: ${proofOfPlay.toString()}');
            final result = await SupabaseService.logProofOfPlay(proofOfPlay);
            debugPrint('LoggingService: Proof of play log result: $result');
          } catch (e) {
            debugPrint('LoggingService: Error logging proof of play: $e');
            // Add back to queue for retry
            _proofOfPlayQueue.add(proofOfPlay);
          }
        }
      }
    } finally {
      // Always reset the processing flag when done
      _isProcessingQueue = false;
    }
  }

  /// Log player start
  void logPlayerStart() {
    if (_screenId == null) {
      debugPrint('LoggingService: Screen ID not available, cannot log player start');
      return;
    }

    final activity = ScreenActivity(
      screenId: _screenId!,
      logDetails: 'Player Start',
      logDatetime: DateTime.now().toUtc(),
    );

    _activityQueue.add(activity);
    debugPrint('LoggingService: Logged player start');

    // Process queue immediately for important events
    _processQueue();
  }

  /// Log file download
  void logFileDownload(String fileName, int fileSize, int currentCount, int totalCount) {
    if (_screenId == null) {
      debugPrint('LoggingService: Screen ID not available, cannot log file download');
      return;
    }

    // Format file size in MB with 1 decimal place
    final fileSizeMB = (fileSize / (1024 * 1024)).toStringAsFixed(1);

    // Create log details string
    final logDetails = 'Downloaded file: $fileName ($fileSizeMB MB) - $currentCount/$totalCount';

    // Check if we already have this exact log in the queue to avoid duplicates
    final hasDuplicate = _activityQueue.any((activity) =>
      activity.logDetails == logDetails &&
      activity.fileDownloadCount == currentCount &&
      activity.totalFileDownload == totalCount
    );

    if (hasDuplicate) {
      debugPrint('LoggingService: Skipping duplicate file download log: $fileName');
      return;
    }

    final activity = ScreenActivity(
      screenId: _screenId!,
      logDetails: logDetails,
      logDatetime: DateTime.now().toUtc(),
      fileDownloadCount: currentCount,
      totalFileDownload: totalCount,
    );

    _activityQueue.add(activity);
    debugPrint('LoggingService: Logged file download: $fileName');

    // Process queue immediately for file download events
    // But only if this is the last file (to avoid excessive processing)
    if (currentCount == totalCount) {
      _processQueue();
    }
  }

  /// Log data update
  void logDataUpdate() {
    if (_screenId == null) {
      debugPrint('LoggingService: Screen ID not available, cannot log data update');
      return;
    }

    // Check if we already have a "Data Update Completed" log in the queue to avoid duplicates
    final hasDuplicateDataUpdate = _activityQueue.any((activity) =>
      activity.logDetails == 'Data Update Completed'
    );

    if (hasDuplicateDataUpdate) {
      debugPrint('LoggingService: Skipping duplicate data update log');
      return;
    }

    final activity = ScreenActivity(
      screenId: _screenId!,
      logDetails: 'Data Update Completed',
      logDatetime: DateTime.now().toUtc(),
    );

    _activityQueue.add(activity);
    debugPrint('LoggingService: Logged data update');

    // Process queue immediately for data update events
    _processQueue();
  }

  /// Log error
  void logError(String errorMessage) {
    if (_screenId == null) {
      debugPrint('LoggingService: Screen ID not available, cannot log error');
      return;
    }

    final activity = ScreenActivity(
      screenId: _screenId!,
      logDetails: 'Error: $errorMessage',
      logDatetime: DateTime.now().toUtc(),
    );

    _activityQueue.add(activity);
    debugPrint('LoggingService: Logged error: $errorMessage');

    // Process queue immediately for error events
    _processQueue();
  }

  /// Log slide display (proof of play)
  void logSlideDisplay(String campaignId, String slideId) {
    if (_screenId == null) {
      debugPrint('LoggingService: Screen ID not available, cannot log slide display');
      return;
    }

    // Validate inputs - ensure we don't pass empty strings for UUID fields
    final validCampaignId = campaignId.isNotEmpty ? campaignId : null;
    final validSlideId = slideId.isNotEmpty ? slideId : null;

    final proofOfPlay = ProofOfPlay(
      screenId: _screenId!,
      campaignId: validCampaignId,
      slideId: validSlideId,
      logDatetime: DateTime.now().toUtc(),
    );

    _proofOfPlayQueue.add(proofOfPlay);
    debugPrint('LoggingService: Logged slide display: $slideId');
  }

  /// Log slide media display (proof of play)
  void logSlideMediaDisplay(String campaignId, String slideId, String mediaId) {
    if (_screenId == null) {
      debugPrint('LoggingService: Screen ID not available, cannot log slide media display');
      return;
    }

    // Validate inputs - ensure we don't pass empty strings for UUID fields
    final validCampaignId = campaignId.isNotEmpty ? campaignId : null;
    final validSlideId = slideId.isNotEmpty ? slideId : null;
    final validMediaId = mediaId.isNotEmpty ? mediaId : null;

    final proofOfPlay = ProofOfPlay(
      screenId: _screenId!,
      campaignId: validCampaignId,
      slideId: validSlideId,
      mediaId: validMediaId,
      logDatetime: DateTime.now().toUtc(),
    );

    _proofOfPlayQueue.add(proofOfPlay);
    debugPrint('LoggingService: Logged slide media display: $mediaId');
  }

  /// Log simple media display (proof of play)
  void logSimpleMediaDisplay(String campaignId, String mediaId) {
    if (_screenId == null) {
      debugPrint('LoggingService: Screen ID not available, cannot log simple media display');
      return;
    }

    // Validate inputs - ensure we don't pass empty strings for UUID fields
    final validCampaignId = campaignId.isNotEmpty ? campaignId : null;
    final validMediaId = mediaId.isNotEmpty ? mediaId : null;

    final proofOfPlay = ProofOfPlay(
      screenId: _screenId!,
      campaignId: validCampaignId,
      mediaId: validMediaId,
      slideId: null, // For simple media, slideId should be null
      logDatetime: DateTime.now().toUtc(),
    );

    _proofOfPlayQueue.add(proofOfPlay);
    debugPrint('LoggingService: Logged simple media display: $mediaId');

    // Process queue immediately for better testing
    _processQueue();
  }

  /// Initialize the logging service with a specific screen ID
  /// This is useful during registration when settings might not be saved yet
  void initialize(String screenId) {
    _screenId = screenId;
    debugPrint('LoggingService: Initialized with screen ID: $_screenId');
  }

  /// Log registration start
  void logRegistrationStart() {
    if (_screenId == null) {
      debugPrint('LoggingService: Screen ID not available, cannot log registration start');
      return;
    }

    final activity = ScreenActivity(
      screenId: _screenId!,
      logDetails: 'Registration Started',
      logDatetime: DateTime.now().toUtc(),
    );

    _activityQueue.add(activity);
    debugPrint('LoggingService: Logged registration start');

    // Process queue immediately for important events
    _processQueue();
  }

  /// Log registration success
  void logRegistrationSuccess() {
    if (_screenId == null) {
      debugPrint('LoggingService: Screen ID not available, cannot log registration success');
      return;
    }

    final activity = ScreenActivity(
      screenId: _screenId!,
      logDetails: 'Registration Completed Successfully',
      logDatetime: DateTime.now().toUtc(),
    );

    _activityQueue.add(activity);
    debugPrint('LoggingService: Logged registration success');

    // Process queue immediately for important events
    _processQueue();
  }

}
