import 'package:flutter/material.dart';
import 'package:signage/app.dart';
import 'package:signage/utils/multi_monitor_handler.dart';
import 'package:signage/utils/platform_utils.dart';
import 'package:media_kit/media_kit.dart';

void main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize media kit for desktop platforms
  if (PlatformUtils.isDesktop) {
    MediaKit.ensureInitialized();
  }

  // Initialize multi-monitor handler
  await MultiMonitorHandler.initialize();

  runApp(const SignageApp());
}
