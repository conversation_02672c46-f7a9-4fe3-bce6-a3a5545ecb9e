import 'dart:convert';

/// Model class representing a schedule item from the Supabase RPC function
class ScheduleItem {
  final String id;
  final String name;
  final int? width;
  final int? height;
  final String? apiUrl;
  final String? bgColor;
  final dynamic content; // Can be null, a List, or a Map
  final String campaignId;
  final int campaignType; // 0 for simple media, 1 for presentation
  final int displayOrder;
  final int? apiDataPreviewDuration;
  
  // Derived properties
  final List<PlaylistItem>? playlistItems;
  
  ScheduleItem({
    required this.id,
    required this.name,
    this.width,
    this.height,
    this.apiUrl,
    this.bgColor,
    this.content,
    required this.campaignId,
    required this.campaignType,
    required this.displayOrder,
    this.apiDataPreviewDuration,
    this.playlistItems,
  });
  
  /// Create a ScheduleItem from a JSON map
  factory ScheduleItem.fromJson(Map<String, dynamic> json) {
    // Extract playlist items if this is a presentation with content
    List<PlaylistItem>? playlistItems;
    
    if (json['campaign_type'] == 1 && json['content'] != null) {
      try {
        // Parse content if it's a string
        final dynamic contentData = json['content'] is String 
            ? jsonDecode(json['content']) 
            : json['content'];
            
        if (contentData is List && contentData.isNotEmpty) {
          // Find playlist content
          for (final item in contentData) {
            if (item['type'] == 'playlist' && 
                item['content'] != null && 
                item['content']['playlist'] != null) {
              
              final playlist = item['content']['playlist'] as List;
              playlistItems = playlist
                  .map((item) => PlaylistItem.fromJson(item))
                  .toList();
              break;
            }
          }
        }
      } catch (e) {
        print('Error parsing playlist items: $e');
      }
    }
    
    return ScheduleItem(
      id: json['id']?.toString() ?? '',
      name: json['name'] ?? '',
      width: json['width'] != null ? int.tryParse(json['width'].toString()) : null,
      height: json['height'] != null ? int.tryParse(json['height'].toString()) : null,
      apiUrl: json['api_url'],
      bgColor: json['bgcolor'],
      content: json['content'],
      campaignId: json['campaignid']?.toString() ?? '',
      campaignType: json['campaign_type'] != null 
          ? int.tryParse(json['campaign_type'].toString()) ?? 0 
          : 0,
      displayOrder: json['display_order'] != null 
          ? int.tryParse(json['display_order'].toString()) ?? 0 
          : 0,
      apiDataPreviewDuration: json['api_data_preview_duration'] != null 
          ? int.tryParse(json['api_data_preview_duration'].toString()) 
          : null,
      playlistItems: playlistItems,
    );
  }
  
  /// Convert ScheduleItem to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'width': width,
      'height': height,
      'api_url': apiUrl,
      'bgcolor': bgColor,
      'content': content,
      'campaignid': campaignId,
      'campaign_type': campaignType,
      'display_order': displayOrder,
      'api_data_preview_duration': apiDataPreviewDuration,
    };
  }
  
  /// Check if this is a simple media item
  bool get isSimpleMedia => campaignType == 0;
  
  /// Check if this is a presentation
  bool get isPresentation => campaignType == 1;
  
  /// Get the file URL for a simple media item
  String? get fileUrl {
    if (isSimpleMedia) {
      // For simple media, the ID is the media ID
      return null; // Will need to be looked up from media list
    }
    return null;
  }
  
  @override
  String toString() {
    return 'ScheduleItem(id: $id, name: $name, campaignType: $campaignType, displayOrder: $displayOrder)';
  }
}

/// Model class representing a playlist item in a presentation
class PlaylistItem {
  final String id;
  final String name;
  final String fileUrl;
  final int duration;
  final String? fileType;
  
  PlaylistItem({
    required this.id,
    required this.name,
    required this.fileUrl,
    required this.duration,
    this.fileType,
  });
  
  /// Create a PlaylistItem from a JSON map
  factory PlaylistItem.fromJson(Map<String, dynamic> json) {
    return PlaylistItem(
      id: json['id']?.toString() ?? '',
      name: json['name'] ?? '',
      fileUrl: json['fileUrl'] ?? '',
      duration: json['duration'] != null 
          ? int.tryParse(json['duration'].toString()) ?? 0 
          : 0,
      fileType: json['fileType'],
    );
  }
  
  /// Convert PlaylistItem to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'fileUrl': fileUrl,
      'duration': duration,
      'fileType': fileType,
    };
  }
  
  @override
  String toString() {
    return 'PlaylistItem(id: $id, name: $name, duration: $duration)';
  }
}
