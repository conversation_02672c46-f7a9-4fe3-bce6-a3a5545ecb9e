import 'package:permission_handler/permission_handler.dart';
import 'package:signage/utils/platform_utils.dart';

class PermissionHandler {
  static Future<bool> requestPermissions() async {
    if (!PlatformUtils.isAndroid) {
      // No permissions needed for desktop platforms
      return true;
    }

    // Handle special permissions first that need separate handling
    await _requestSpecialPermissions();

    // Request standard Android permissions
    Map<Permission, PermissionStatus> statuses = await [
      Permission.storage,
      Permission.location,
      Permission.locationWhenInUse,
      Permission.notification,
    ].request();

    // We don't include locationAlways in the main request as it's not required
    // and we don't want to force users to grant it

    // Check if all permissions are granted
    bool allGranted = true;
    List<Permission> notGrantedPermissions = [];

    statuses.forEach((permission, status) {
      if (!status.isGranted) {
        allGranted = false;
        notGrantedPermissions.add(permission);
      }
    });

    // Log which permissions were not granted
    if (notGrantedPermissions.isNotEmpty) {
      print('The following permissions were not granted:');
      for (var permission in notGrantedPermissions) {
        print('- $permission: ${statuses[permission]}');
      }
    }

    // Check special permissions status
    bool specialPermissionsGranted = await _areSpecialPermissionsGranted();

    return allGranted && specialPermissionsGranted;
  }

  /// Request special permissions that need separate handling
  static Future<void> _requestSpecialPermissions() async {
    // Request ignore battery optimizations
    await requestIgnoreBatteryOptimizations();

    // Request manage external storage
    await requestManageExternalStorage();

    // Request system alert window
    await requestSystemAlertWindow();

    // Request notification policy access
    await requestAccessNotificationPolicy();

    // Request media location access
    await requestAccessMediaLocation();

    // Request location permissions
    await requestLocationPermissions();
  }

  /// Open app settings to allow the user to manually grant permissions
  static Future<bool> openSettings() async {
    return await openAppSettings();
  }

  /// Check if a specific permission is granted
  static Future<bool> isPermissionGranted(Permission permission) async {
    return await permission.isGranted;
  }

  /// Check if all required permissions are granted
  static Future<bool> areAllPermissionsGranted() async {
    if (!PlatformUtils.isAndroid) {
      return true;
    }

    // Check all required permissions
    bool storage = await Permission.storage.isGranted;
    bool manageExternalStorage = await Permission.manageExternalStorage.isGranted;
    bool location = await Permission.location.isGranted;
    bool locationWhenInUse = await Permission.locationWhenInUse.isGranted;
    bool installPackages = await Permission.requestInstallPackages.isGranted;
    bool batteryOptimization = await Permission.ignoreBatteryOptimizations.isGranted;
    bool systemAlertWindow = await Permission.systemAlertWindow.isGranted;
    bool accessNotificationPolicy = await Permission.accessNotificationPolicy.isGranted;
    bool accessMediaLocation = await Permission.accessMediaLocation.isGranted;

    // We don't check locationAlways as it's not required for the app to function

    return storage && manageExternalStorage && location && locationWhenInUse &&
           installPackages && batteryOptimization && systemAlertWindow &&
           accessNotificationPolicy && accessMediaLocation;
  }

  /// Check if all special permissions are granted
  static Future<bool> _areSpecialPermissionsGranted() async {
    bool batteryOptimization = await Permission.ignoreBatteryOptimizations.isGranted;
    bool manageExternalStorage = await Permission.manageExternalStorage.isGranted;
    bool systemAlertWindow = await Permission.systemAlertWindow.isGranted;
    bool accessNotificationPolicy = await Permission.accessNotificationPolicy.isGranted;
    bool accessMediaLocation = await Permission.accessMediaLocation.isGranted;
    bool locationWhenInUse = await Permission.locationWhenInUse.isGranted;

    // Log the status of each permission for debugging
    print('Permission status:');
    print('- Battery Optimization: $batteryOptimization');
    print('- Manage External Storage: $manageExternalStorage');
    print('- System Alert Window: $systemAlertWindow');
    print('- Access Notification Policy: $accessNotificationPolicy');
    print('- Access Media Location: $accessMediaLocation');
    print('- Location When In Use: $locationWhenInUse');

    // We don't require locationAlways to be granted, only locationWhenInUse
    // This is because most apps only need location while they're being used

    return batteryOptimization && manageExternalStorage && systemAlertWindow &&
           accessNotificationPolicy && accessMediaLocation && locationWhenInUse;
  }

  /// Request location permissions
  static Future<bool> requestLocationPermissions() async {
    // First request the basic location permission
    if (await Permission.location.isDenied) {
      await Permission.location.request();
    }

    // Then request the more specific locationWhenInUse permission
    if (await Permission.locationWhenInUse.isDenied) {
      await Permission.locationWhenInUse.request();

      // If still denied, send to settings
      if (await Permission.locationWhenInUse.isDenied) {
        return openAppSettings();
      }
    }

    // We don't force locationAlways as it's not typically required
    // and many users won't want to grant it

    return await Permission.locationWhenInUse.isGranted;
  }

  /// Request ignore battery optimizations permission
  static Future<bool> requestIgnoreBatteryOptimizations() async {
    if (await Permission.ignoreBatteryOptimizations.isDenied) {
      await Permission.ignoreBatteryOptimizations.request();
    }
    return await Permission.ignoreBatteryOptimizations.isGranted;
  }

  /// Request manage external storage permission
  static Future<bool> requestManageExternalStorage() async {
    if (await Permission.manageExternalStorage.isDenied) {
      await Permission.manageExternalStorage.request();

      // If still denied, send to settings
      if (await Permission.manageExternalStorage.isDenied) {
        return openAppSettings();
      }
    }
    return await Permission.manageExternalStorage.isGranted;
  }

  /// Request system alert window permission
  static Future<bool> requestSystemAlertWindow() async {
    // This permission requires sending user to settings on newer Android versions
    if (await Permission.systemAlertWindow.isDenied) {
      // First try requesting directly
      await Permission.systemAlertWindow.request();

      // If still denied, send to settings
      if (await Permission.systemAlertWindow.isDenied) {
        return openAppSettings();
      }
    }
    return await Permission.systemAlertWindow.isGranted;
  }

  /// Request access notification policy permission
  static Future<bool> requestAccessNotificationPolicy() async {
    if (await Permission.accessNotificationPolicy.isDenied) {
      await Permission.accessNotificationPolicy.request();

      // If still denied, send to settings
      if (await Permission.accessNotificationPolicy.isDenied) {
        return openAppSettings();
      }
    }
    return await Permission.accessNotificationPolicy.isGranted;
  }

  /// Request access media location permission
  static Future<bool> requestAccessMediaLocation() async {
    if (await Permission.accessMediaLocation.isDenied) {
      await Permission.accessMediaLocation.request();

      // If still denied, send to settings
      if (await Permission.accessMediaLocation.isDenied) {
        return openAppSettings();
      }
    }
    return await Permission.accessMediaLocation.isGranted;
  }

  /// Get instructions for enabling specific permissions from settings
  static String getPermissionInstructions(Permission permission) {
    switch (permission) {
      case Permission.ignoreBatteryOptimizations:
        return "To disable battery optimization:\n"
               "1. Go to Settings > Apps > Your App\n"
               "2. Tap on 'Battery'\n"
               "3. Select 'Don't optimize'";

      case Permission.manageExternalStorage:
        return "To allow storage management:\n"
               "1. Go to Settings > Apps > Your App\n"
               "2. Tap on 'Permissions'\n"
               "3. Tap on 'Files and media' or 'Storage'\n"
               "4. Select 'Allow management of all files'";

      case Permission.systemAlertWindow:
        return "To allow display over other apps:\n"
               "1. Go to Settings > Apps > Your App\n"
               "2. Tap on 'Display over other apps' or 'Appear on top'\n"
               "3. Toggle the permission on";

      case Permission.accessNotificationPolicy:
        return "To allow notification policy access:\n"
               "1. Go to Settings > Sound & notification > Do Not Disturb access\n"
               "2. Find your app and toggle the permission on";

      case Permission.accessMediaLocation:
        return "To allow media location access:\n"
               "1. Go to Settings > Apps > Your App\n"
               "2. Tap on 'Permissions'\n"
               "3. Tap on 'Location'\n"
               "4. Select 'Allow all the time'";

      case Permission.location:
      case Permission.locationWhenInUse:
        return "To allow location access:\n"
               "1. Go to Settings > Apps > Your App\n"
               "2. Tap on 'Permissions'\n"
               "3. Tap on 'Location'\n"
               "4. Select 'Allow only while using the app'";

      case Permission.locationAlways:
        return "Note: This app works with 'Allow only while using the app' location permission.\n"
               "You don't need to enable 'Allow all the time' for normal operation.";

      default:
        return "Please enable this permission from the app settings.";
    }
  }
}
