import 'dart:convert';

/// Model class representing a proof of play log entry in the Supabase 'proof_of_play' table
class ProofOfPlay {
  final int? id;
  final String screenId;
  final String? campaignId;
  final String? slideId;
  final String? mediaId;
  final DateTime logDatetime;

  ProofOfPlay({
    this.id,
    required this.screenId,
    this.campaignId,
    this.slideId,
    this.mediaId,
    required this.logDatetime,
  });

  /// Create a ProofOfPlay from a JSON map
  factory ProofOfPlay.fromJson(Map<String, dynamic> json) {
    return ProofOfPlay(
      id: json['id'],
      screenId: json['screen_id']?.toString() ?? '',
      campaignId: json['campaign_id']?.toString(),
      slideId: json['slide_id']?.toString(),
      mediaId: json['media_id']?.toString(),
      logDatetime: json['log_datetime'] != null
          ? DateTime.parse(json['log_datetime'])
          : DateTime.now().toUtc(),
    );
  }

  /// Convert ProofOfPlay to a JSON map
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {
      // Exclude id field as it's an identity column in Supabase
      'screen_id': screenId,
      'log_datetime': logDatetime.toIso8601String(),
    };

    // Only include non-empty values for UUID fields
    // Empty strings will be converted to null in the SupabaseService.logProofOfPlay method
    if (campaignId != null && campaignId!.isNotEmpty) {
      json['campaign_id'] = campaignId;
    } else {
      json['campaign_id'] = null;
    }

    if (slideId != null && slideId!.isNotEmpty) {
      json['slide_id'] = slideId;
    } else {
      json['slide_id'] = null;
    }

    if (mediaId != null && mediaId!.isNotEmpty) {
      json['media_id'] = mediaId;
    } else {
      json['media_id'] = null;
    }

    return json;
  }

  @override
  String toString() {
    return 'ProofOfPlay(id: $id, screenId: $screenId, campaignId: $campaignId, slideId: $slideId, mediaId: $mediaId, logDatetime: $logDatetime)';
  }
}
