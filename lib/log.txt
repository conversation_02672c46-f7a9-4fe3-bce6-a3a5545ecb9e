flutter: ====================================================
flutter: Checking campaign triggers before moving to next schedule item
flutter: === CAMPAIGN TRIGGER CHECK START ===
flutter: Current campaign index: 1
flutter: Total campaigns: 2
flutter: Checking next campaign in sequence: 0: General Campaign (trigger_type: 1)
flutter: Default loop campaign: General Campaign - displaying
flutter: Campaign General Campaign meets trigger conditions!
flutter: Moving from campaign 1 to 0
flutter: New campaign: General Campaign
flutter: === CAMPAIGN TRIGGER CHECK END ===
flutter: Moving from schedule item 1 to 2
flutter: New schedule item: nzdeliveryapp_digi-eyeline-1920x540.png
flutter: ====================================================
flutter: Playing next schedule item: nzdeliveryapp_digi-eyeline-1920x540.png
flutter: ID: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: ====================================================
flutter: ====================================================
flutter: Starting transition to next content: nzdeliveryapp_digi-eyeline-1920x540.png
flutter: Current content: 104a97fe-2928-4a35-b785-fbdc83765845
flutter: Next content: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: ====================================================
flutter: ====================================================
flutter: Current widget that has finished playing:
flutter: Name: nzdeliveryapp_digi-eyeline-1920x540.png
flutter: ID: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: Type: SimpleMedia
flutter: ====================================================
flutter: ====================================================
flutter: Creating SimpleMediaWidget for nzdeliveryapp_digi-eyeline-1920x540.png
flutter: ID: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: File Path: /home/<USER>/Documents/signage/content/nzdeliveryapp_digi-eyeline-1920x540.png
flutter: Is Image: true, Is Video: false
flutter: ====================================================
flutter: Storing content widget for schedule item: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: LoggingService: Logged simple media display: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: LoggingService: Processing queue - Activities: 0, Proof of Play: 1
flutter: LoggingService: Processing 1 proof of play logs
flutter: LoggingService: Sending proof of play log to Supabase: ProofOfPlay(id: null, screenId: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: null, mediaId: 75e20d80-47ed-4274-aca9-0251f2a182b5, logDatetime: 2025-05-31 11:48:56.212264Z)
flutter: [SupabaseService] Logging proof of play: ProofOfPlay(id: null, screenId: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: null, mediaId: 75e20d80-47ed-4274-aca9-0251f2a182b5, logDatetime: 2025-05-31 11:48:56.212264Z)
flutter: [SupabaseService] Proof of play data: {screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, log_datetime: 2025-05-31T11:48:56.212264Z, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: null, media_id: 75e20d80-47ed-4274-aca9-0251f2a182b5}
flutter: Starting transition animation
flutter: Displaying content widget for schedule item: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: SimpleMediaWidget: initState for nzdeliveryapp_digi-eyeline-1920x540.png
flutter: ImageTimerWidget: initState for /home/<USER>/Documents/signage/content/nzdeliveryapp_digi-eyeline-1920x540.png
flutter: ImageTimerWidget: starting timer for /home/<USER>/Documents/signage/content/nzdeliveryapp_digi-eyeline-1920x540.png with duration 8 seconds
flutter: ImageTimerWidget: image loaded for /home/<USER>/Documents/signage/content/nzdeliveryapp_digi-eyeline-1920x540.png
flutter: Displaying content widget for schedule item: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: Displaying content widget for schedule item: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: Displaying content widget for schedule item: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: ====================================================
flutter: Completing transition
flutter: Current content ID: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: Next content ID: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: ====================================================
flutter: [SupabaseService] Proof of play log response: [{id: 994, screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: null, media_id: 75e20d80-47ed-4274-aca9-0251f2a182b5, log_datetime: 2025-05-31T11:48:56.212264+00:00}]
flutter: LoggingService: Proof of play log result: true
flutter: Displaying content widget for schedule item: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: ImageTimerWidget: image loaded for /home/<USER>/Documents/signage/content/nzdeliveryapp_digi-eyeline-1920x540.png
flutter: PlatformVideoWidget: Disposing video player
flutter: SimpleMediaWidget: dispose for FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
media_kit: VideoOutput: video_output_dispose: 129915520202016
flutter: Safety timer: disposing old content widget: 104a97fe-2928-4a35-b785-fbdc83765845
flutter: ====================================================
flutter: Disposing content widget for schedule item: 104a97fe-2928-4a35-b785-fbdc83765845
flutter: Current active schedule item ID: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: Current schedule item ID: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: Content widgets in memory: 104a97fe-2928-4a35-b785-fbdc83765845, 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: Content widget disposed for schedule item: 104a97fe-2928-4a35-b785-fbdc83765845
flutter: Remaining widgets in memory: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: ====================================================
flutter: LoggingService: Processing queue - Activities: 0, Proof of Play: 0
flutter: ImageTimerWidget: timer completed for /home/<USER>/Documents/signage/content/nzdeliveryapp_digi-eyeline-1920x540.png
flutter: ====================================================
flutter: Content completed, moving to next item
flutter: Completed widget details:
flutter: Name: nzdeliveryapp_digi-eyeline-1920x540.png
flutter: ID: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: Type: SimpleMedia
flutter: Media Type: Image
flutter: ====================================================
flutter: Checking campaign triggers before moving to next schedule item
flutter: === CAMPAIGN TRIGGER CHECK START ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking next campaign in sequence: 1: Weather Campaign (trigger_type: 3)
flutter: Using Linux-specific location service
flutter: Location obtained from IP: 21.1959, 72.8302
flutter: Fetching weather data from: https://api.openweathermap.org/data/2.5/weather?lat=21.1959&lon=72.8302&appid=29b1d3623a51675e8ed3c1d3fa13c0f0&units=metric
flutter: Weather API response: 31.96°C
flutter: Weather check: campaign=Weather Campaign, temp=31.96, range=20.0-40.0, inRange=true
flutter: Campaign Weather Campaign meets trigger conditions!
flutter: Moving from campaign 0 to 1
flutter: New campaign: Weather Campaign
flutter: === CAMPAIGN TRIGGER CHECK END ===
flutter: Moving from schedule item 2 to 3
flutter: New schedule item: 37327 Skinny Eyeline digital 1920x540.mp4
flutter: ====================================================
flutter: Playing next schedule item: 37327 Skinny Eyeline digital 1920x540.mp4
flutter: ID: d696c03d-c48b-420e-9857-917a226024c4
flutter: ====================================================
flutter: ====================================================
flutter: Starting transition to next content: 37327 Skinny Eyeline digital 1920x540.mp4
flutter: Current content: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: Next content: d696c03d-c48b-420e-9857-917a226024c4
flutter: ====================================================
flutter: ====================================================
flutter: Current widget that has finished playing:
flutter: Name: 37327 Skinny Eyeline digital 1920x540.mp4
flutter: ID: d696c03d-c48b-420e-9857-917a226024c4
flutter: Type: SimpleMedia
flutter: ====================================================
flutter: ====================================================
flutter: Creating SimpleMediaWidget for 37327 Skinny Eyeline digital 1920x540.mp4
flutter: ID: d696c03d-c48b-420e-9857-917a226024c4
flutter: File Path: /home/<USER>/Documents/signage/content/37327 Skinny Eyeline digital 1920x540.mp4
flutter: Is Image: false, Is Video: true
flutter: ====================================================
flutter: Storing content widget for schedule item: d696c03d-c48b-420e-9857-917a226024c4
flutter: LoggingService: Logged simple media display: d696c03d-c48b-420e-9857-917a226024c4
flutter: LoggingService: Processing queue - Activities: 0, Proof of Play: 1
flutter: LoggingService: Processing 1 proof of play logs
flutter: LoggingService: Sending proof of play log to Supabase: ProofOfPlay(id: null, screenId: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaignId: 3cc80b6d-18d5-446a-b785-979c950c4d92, slideId: null, mediaId: d696c03d-c48b-420e-9857-917a226024c4, logDatetime: 2025-05-31 11:49:04.696823Z)
flutter: [SupabaseService] Logging proof of play: ProofOfPlay(id: null, screenId: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaignId: 3cc80b6d-18d5-446a-b785-979c950c4d92, slideId: null, mediaId: d696c03d-c48b-420e-9857-917a226024c4, logDatetime: 2025-05-31 11:49:04.696823Z)
flutter: [SupabaseService] Proof of play data: {screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, log_datetime: 2025-05-31T11:49:04.696823Z, campaign_id: 3cc80b6d-18d5-446a-b785-979c950c4d92, slide_id: null, media_id: d696c03d-c48b-420e-9857-917a226024c4}
flutter: Starting transition animation
flutter: Displaying content widget for schedule item: d696c03d-c48b-420e-9857-917a226024c4
flutter: SimpleMediaWidget: initState for 37327 Skinny Eyeline digital 1920x540.mp4
flutter: ImageTimerWidget: image loaded for /home/<USER>/Documents/signage/content/nzdeliveryapp_digi-eyeline-1920x540.png
flutter: Displaying content widget for schedule item: d696c03d-c48b-420e-9857-917a226024c4
flutter: [SupabaseService] Proof of play log response: [{id: 995, screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaign_id: 3cc80b6d-18d5-446a-b785-979c950c4d92, slide_id: null, media_id: d696c03d-c48b-420e-9857-917a226024c4, log_datetime: 2025-05-31T11:49:04.696823+00:00}]
flutter: LoggingService: Proof of play log result: true
flutter: Displaying content widget for schedule item: d696c03d-c48b-420e-9857-917a226024c4
flutter: Displaying content widget for schedule item: d696c03d-c48b-420e-9857-917a226024c4
media_kit: VideoOutput: video_output_new: 129915520269408
flutter: ====================================================
flutter: Completing transition
flutter: Current content ID: d696c03d-c48b-420e-9857-917a226024c4
flutter: Next content ID: d696c03d-c48b-420e-9857-917a226024c4
flutter: ====================================================
Cannot load libcuda.so.1
media_kit: VideoOutput: Using H/W rendering.
flutter: VideoOutput.Resize
flutter: {handle: 129915520269408, id: 105105486975984, rect: {left: 0, top: 0, width: 1, height: 1}}
flutter: NativeVideoController: Texture ID: 105105486975984
flutter: Displaying content widget for schedule item: d696c03d-c48b-420e-9857-917a226024c4
flutter: ImageTimerWidget: dispose for /home/<USER>/Documents/signage/content/nzdeliveryapp_digi-eyeline-1920x540.png
flutter: ImageTimerWidget: canceling timer for /home/<USER>/Documents/signage/content/nzdeliveryapp_digi-eyeline-1920x540.png
flutter: SimpleMediaWidget: dispose for nzdeliveryapp_digi-eyeline-1920x540.png
media_kit: TextureGL: Resize: (1920, 540)
flutter: VideoOutput.Resize
flutter: {handle: 129915520269408, id: 105105486975984, rect: {left: 0, top: 0, width: 1920, height: 540}}
flutter: Safety timer: disposing old content widget: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: ====================================================
flutter: Disposing content widget for schedule item: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: Current active schedule item ID: d696c03d-c48b-420e-9857-917a226024c4
flutter: Current schedule item ID: d696c03d-c48b-420e-9857-917a226024c4
flutter: Content widgets in memory: 75e20d80-47ed-4274-aca9-0251f2a182b5, d696c03d-c48b-420e-9857-917a226024c4
flutter: Content widget disposed for schedule item: 75e20d80-47ed-4274-aca9-0251f2a182b5
flutter: Remaining widgets in memory: d696c03d-c48b-420e-9857-917a226024c4
flutter: ====================================================
flutter: PlatformVideoWidget: Desktop video completed
flutter: ====================================================
flutter: Content completed, moving to next item
flutter: Completed widget details:
flutter: Name: 37327 Skinny Eyeline digital 1920x540.mp4
flutter: ID: d696c03d-c48b-420e-9857-917a226024c4
flutter: Type: SimpleMedia
flutter: Media Type: Video
flutter: ====================================================
flutter: Checking campaign triggers before moving to next schedule item
flutter: === CAMPAIGN TRIGGER CHECK START ===
flutter: Current campaign index: 1
flutter: Total campaigns: 2
flutter: Checking next campaign in sequence: 0: General Campaign (trigger_type: 1)
flutter: Default loop campaign: General Campaign - displaying
flutter: Campaign General Campaign meets trigger conditions!
flutter: Moving from campaign 1 to 0
flutter: New campaign: General Campaign
flutter: === CAMPAIGN TRIGGER CHECK END ===
flutter: Finished all schedule items for campaign General Campaign
flutter: Moving to next campaign...
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Weather Campaign (trigger_type: 3)
flutter: Using Linux-specific location service
flutter: Location obtained from IP: 21.1959, 72.8302
flutter: Fetching weather data from: https://api.openweathermap.org/data/2.5/weather?lat=21.1959&lon=72.8302&appid=29b1d3623a51675e8ed3c1d3fa13c0f0&units=metric
flutter: Weather API response: 31.96°C
flutter: Weather check: campaign=Weather Campaign, temp=31.96, range=20.0-40.0, inRange=true
flutter: Campaign Weather Campaign meets trigger conditions!
flutter: Loaded 1 schedule items for campaign Weather Campaign
flutter:   [0] Weather
flutter: === MOVED TO CAMPAIGN Weather Campaign ===
flutter: ====================================================
flutter: Playing next schedule item: Weather
flutter: ID: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: ====================================================
flutter: ====================================================
flutter: Starting transition to next content: Weather
flutter: Current content: d696c03d-c48b-420e-9857-917a226024c4
flutter: Next content: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: ====================================================
flutter: ====================================================
flutter: Current widget that has finished playing:
flutter: Name: Weather
flutter: ID: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: Type: SlideShow
flutter: ====================================================
flutter: ====================================================
flutter: Creating SlideShowWidget for Weather
flutter: ID: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: ====================================================
flutter: Storing content widget for schedule item: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: LoggingService: Logged slide display: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: Starting transition animation
flutter: Displaying content widget for schedule item: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: SlideShowWidget: initState for Weather
flutter: SlideShowWidget: Building content with design dimensions: 1920.0 x 1080.0
flutter: SlideShowWidget: Actual screen dimensions: 1920.0 x 1080.0
flutter: SlideShowWidget: Original playlist position: (0.0, 0.0), size: 1920.0 x 1080.0, zIndex: 1
flutter: SlideShowWidget: Expected scaled position: (0.0, 0.0), size: 1920.0 x 1080.0
flutter: SlideShowWidget: Original API position: (809.828125, 214.828125), size: 876.25 x 387.5, zIndex: 3
flutter: SlideShowWidget: Creating GLOBAL API data controller for all APIWidgets in this slide
flutter: ApiDataController: Created with apiUrl: https://api.openweathermap.org/data/2.5/weather?lat=17&lon=17&appid=29b1d3623a51675e8ed3c1d3fa13c0f0&units=metric, apiDataPreviewDuration: 0
flutter: ApiDataController: Fetching data from https://api.openweathermap.org/data/2.5/weather?lat=17&lon=17&appid=29b1d3623a51675e8ed3c1d3fa13c0f0&units=metric
flutter: ApiDataController: Setting up refresh timer for every 5 minutes
flutter: SlideShowWidget: GLOBAL API data controller created with apiDataPreviewDuration: 0
flutter: SlideShowWidget: Expected scaled API position: (809.828125, 214.828125), size: 876.25 x 387.5
flutter: SlideShowWidget: Original API position: (824.15625, 634.328125), size: 858.75 x 117.5, zIndex: 4
flutter: SlideShowWidget: Expected scaled API position: (824.15625, 634.328125), size: 858.75 x 117.5
flutter: SlideShowWidget: Original API position: (263.32916259765625, 258.32509358723956), size: 500.0 x 500.0, zIndex: 5
flutter: SlideShowWidget: Expected scaled API position: (263.32916259765625, 258.32509358723956), size: 500.0 x 500.0
flutter: SlideShowWidget: Player screen size: 1920.0x1080.0
flutter: SlideShowWidget: Design size: 1920.0 x 1080.0
flutter: SlideShowWidget: Scaling factors: horizontal=1.0, vertical=1.0
flutter: SlideShowWidget: Building scaled widgets with:
flutter:   - Design dimensions: 1920.0 x 1080.0
flutter:   - Screen dimensions: 1920.0 x 1080.0
flutter:   - Scale factors: 1.0 x 1.0
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=0.0, top=0.0, width=1920.0, height=1080.0
flutter:   - Scaled: left=0.0, top=0.0, width=1920.0, height=1080.0
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=809.828125, top=214.828125, width=876.25, height=387.5
flutter:   - Scaled: left=809.828125, top=214.828125, width=876.25, height=387.5
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=824.15625, top=634.328125, width=858.75, height=117.5
flutter:   - Scaled: left=824.15625, top=634.328125, width=858.75, height=117.5
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=263.32916259765625, top=258.32509358723956, width=500.0, height=500.0
flutter:   - Scaled: left=263.32916259765625, top=258.32509358723956, width=500.0, height=500.0
flutter: PlaylistWidget: initState for 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: PlaylistWidget: loading item weather-cloud.mp4 at index 0
flutter: APIWidget: initState for 5e11d095-702b-457c-bcec-db2adc7e5e91
flutter: APIWidget: Building widget with state - isLoading: true, hasError: false, isInitialized: false, contentWidgets: 0
flutter: APIWidget: Dimensions - width: 876.25, height: 387.5
flutter: APIWidget: Showing loading state
flutter: APIWidget: initState for 27a76786-c68e-4be7-9857-826c3a968cc4
flutter: APIWidget: Building widget with state - isLoading: true, hasError: false, isInitialized: false, contentWidgets: 0
flutter: APIWidget: Dimensions - width: 858.75, height: 117.5
flutter: APIWidget: Showing loading state
flutter: APIWidget: initState for dce9c75d-bae9-4160-ac4d-0d274fe8dc96
flutter: APIWidget: Building widget with state - isLoading: true, hasError: false, isInitialized: false, contentWidgets: 0
flutter: APIWidget: Dimensions - width: 500.0, height: 500.0
flutter: APIWidget: Showing loading state
flutter: Displaying content widget for schedule item: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: PlaylistWidget: Building with dimensions 1920.0 x 1080.0
flutter: Displaying content widget for schedule item: 80b479a8-d300-46ae-97d0-59653e52d73f
media_kit: VideoOutput: video_output_new: 129915519465232
flutter: Displaying content widget for schedule item: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: ====================================================
Cannot load libcuda.so.1
flutter: Completing transition
media_kit: VideoOutput: Using H/W rendering.
flutter: Current content ID: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: Next content ID: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: ====================================================
flutter: VideoOutput.Resize
flutter: {handle: 129915519465232, id: 105105492388688, rect: {left: 0, top: 0, width: 1, height: 1}}
flutter: NativeVideoController: Texture ID: 105105492388688
flutter: Displaying content widget for schedule item: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: SlideShowWidget: Player screen size: 1920.0x1080.0
flutter: SlideShowWidget: Design size: 1920.0 x 1080.0
flutter: SlideShowWidget: Scaling factors: horizontal=1.0, vertical=1.0
flutter: SlideShowWidget: Building scaled widgets with:
flutter:   - Design dimensions: 1920.0 x 1080.0
flutter:   - Screen dimensions: 1920.0 x 1080.0
flutter:   - Scale factors: 1.0 x 1.0
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=0.0, top=0.0, width=1920.0, height=1080.0
flutter:   - Scaled: left=0.0, top=0.0, width=1920.0, height=1080.0
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=809.828125, top=214.828125, width=876.25, height=387.5
flutter:   - Scaled: left=809.828125, top=214.828125, width=876.25, height=387.5
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=824.15625, top=634.328125, width=858.75, height=117.5
flutter:   - Scaled: left=824.15625, top=634.328125, width=858.75, height=117.5
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=263.32916259765625, top=258.32509358723956, width=500.0, height=500.0
flutter:   - Scaled: left=263.32916259765625, top=258.32509358723956, width=500.0, height=500.0
flutter: PlaylistWidget: Building with dimensions 1920.0 x 1080.0
flutter: APIWidget: Building widget with state - isLoading: true, hasError: false, isInitialized: false, contentWidgets: 0
flutter: APIWidget: Dimensions - width: 876.25, height: 387.5
flutter: APIWidget: Showing loading state
flutter: APIWidget: Building widget with state - isLoading: true, hasError: false, isInitialized: false, contentWidgets: 0
flutter: APIWidget: Dimensions - width: 858.75, height: 117.5
flutter: APIWidget: Showing loading state
flutter: APIWidget: Building widget with state - isLoading: true, hasError: false, isInitialized: false, contentWidgets: 0
flutter: APIWidget: Dimensions - width: 500.0, height: 500.0
flutter: APIWidget: Showing loading state
flutter: PlatformVideoWidget: Disposing video player
flutter: SimpleMediaWidget: dispose for 37327 Skinny Eyeline digital 1920x540.mp4
media_kit: VideoOutput: video_output_dispose: 129915520269408
flutter: ApiDataController: Checking if all widgets are ready - dataReady: false, videoWidgets: 0
flutter: SlideShowWidget: Not all APIWidgets are ready yet, waiting...
media_kit: TextureGL: Resize: (1920, 1080)
flutter: VideoOutput.Resize
flutter: {handle: 129915519465232, id: 105105492388688, rect: {left: 0, top: 0, width: 1920, height: 1080}}
flutter: SlideShowWidget: Starting global timer regardless of widget state
flutter: ApiDataController: Starting global timer for all APIWidgets
flutter: ApiDataController: Starting GLOBAL display timer - videoWidgets: 0, apiDataPreviewDuration: 0
flutter: ApiDataController: Starting GLOBAL display timer for 15 seconds (no videos)
flutter: APIWidget: Controller update received for 5e11d095-702b-457c-bcec-db2adc7e5e91
flutter: APIWidget: Controller update received for 27a76786-c68e-4be7-9857-826c3a968cc4
flutter: APIWidget: Controller update received for dce9c75d-bae9-4160-ac4d-0d274fe8dc96
flutter: APIWidget: Building widget with state - isLoading: true, hasError: false, isInitialized: false, contentWidgets: 0
flutter: APIWidget: Dimensions - width: 876.25, height: 387.5
flutter: APIWidget: Showing loading state
flutter: APIWidget: Building widget with state - isLoading: true, hasError: false, isInitialized: false, contentWidgets: 0
flutter: APIWidget: Dimensions - width: 858.75, height: 117.5
flutter: APIWidget: Showing loading state
flutter: APIWidget: Building widget with state - isLoading: true, hasError: false, isInitialized: false, contentWidgets: 0
flutter: APIWidget: Dimensions - width: 500.0, height: 500.0
flutter: APIWidget: Showing loading state
flutter: ApiDataController: API Response received with status 200
flutter: ApiDataController: Received 1 records from API
flutter: ApiDataController: API data fetched successfully, waiting for all APIWidgets to initialize
flutter: APIWidget: Controller update received for 5e11d095-702b-457c-bcec-db2adc7e5e91
flutter: APIWidget: Building content widgets for record index 0
flutter: APIWidget: Processing content item: {style: {color: rgba(255, 255, 255, 1), fontSize: 120px, textAlign: center, fontFamily: Helvetica, sans-serif, fontWeight: bold, verticalAlign: middle, backgroundColor: transparent}, subtype: api.text, dataField: weather.description, apiMapping: {weather.description: true}, placeholder: API Data, currentIndex: 0, placeholderUrl: }
flutter: APIWidget: Content item details - subtype: api.text, dataField: weather.description, placeholderUrl: 
flutter: APIWidget: Extracting field "weather.description" (parts: weather, description)
flutter: APIWidget: Found value for "weather.description": clear sky (String)
flutter: APIWidget: Widget dimensions - width: 876.25, height: 387.5
flutter: APIWidget: Building text widget with dataValue: clear sky (String)
flutter: APIWidget: Content config: {style: {color: rgba(255, 255, 255, 1), fontSize: 120px, textAlign: center, fontFamily: Helvetica, sans-serif, fontWeight: bold, verticalAlign: middle, backgroundColor: transparent}, subtype: api.text, dataField: weather.description, apiMapping: {weather.description: true}, placeholder: API Data, currentIndex: 0, placeholderUrl: }
flutter: APIWidget: Text content: "clear sky"
flutter: APIWidget: Text style: {color: rgba(255, 255, 255, 1), fontSize: 120px, textAlign: center, fontFamily: Helvetica, sans-serif, fontWeight: bold, verticalAlign: middle, backgroundColor: transparent}
flutter: APIWidget: Parsing color: rgba(255, 255, 255, 1)
flutter: APIWidget: Parsed rgba color: rgba(255, 255, 255, 1) -> Color(alpha: 1.0000, red: 1.0000, green: 1.0000, blue: 1.0000, colorSpace: ColorSpace.sRGB)
flutter: APIWidget: Text color from string: rgba(255, 255, 255, 1) -> Color(alpha: 1.0000, red: 1.0000, green: 1.0000, blue: 1.0000, colorSpace: ColorSpace.sRGB)
flutter: APIWidget: Raw fontSize value: 120px (String)
flutter: APIWidget: Parsed font size: 120.0
flutter: APIWidget: Scaled font size: 43.05555555555556 (scale factor: 0.3587962962962963)
flutter: APIWidget: Font weight: bold -> FontWeight.w700
flutter: APIWidget: Text alignment: center -> TextAlign.center
flutter: APIWidget: Creating text widget with content: "clear sky" at size 876.25 x 387.5
flutter: APIWidget: Added api.text widget with size 876.25x387.5
flutter: APIWidget: Built 1 content widgets
flutter: APIWidget: Controller update received for 27a76786-c68e-4be7-9857-826c3a968cc4
flutter: APIWidget: Building content widgets for record index 0
flutter: APIWidget: Processing content item: {style: {color: rgba(255, 255, 255, 1), fontSize: 96px, textAlign: center, fontFamily: Helvetica, sans-serif, fontWeight: bold, verticalAlign: middle, backgroundColor: transparent}, subtype: api.text, dataField: main.temp, apiMapping: {main.temp: true}, placeholder: API Data, currentIndex: 0, placeholderUrl: }
flutter: APIWidget: Content item details - subtype: api.text, dataField: main.temp, placeholderUrl: 
flutter: APIWidget: Extracting field "main.temp" (parts: main, temp)
flutter: APIWidget: Found value for "main.temp": 42.35 (double)
flutter: APIWidget: Widget dimensions - width: 858.75, height: 117.5
flutter: APIWidget: Building text widget with dataValue: 42.35 (double)
flutter: APIWidget: Content config: {style: {color: rgba(255, 255, 255, 1), fontSize: 96px, textAlign: center, fontFamily: Helvetica, sans-serif, fontWeight: bold, verticalAlign: middle, backgroundColor: transparent}, subtype: api.text, dataField: main.temp, apiMapping: {main.temp: true}, placeholder: API Data, currentIndex: 0, placeholderUrl: }
flutter: APIWidget: Text content: "42.35"
flutter: APIWidget: Text style: {color: rgba(255, 255, 255, 1), fontSize: 96px, textAlign: center, fontFamily: Helvetica, sans-serif, fontWeight: bold, verticalAlign: middle, backgroundColor: transparent}
flutter: APIWidget: Parsing color: rgba(255, 255, 255, 1)
flutter: APIWidget: Parsed rgba color: rgba(255, 255, 255, 1) -> Color(alpha: 1.0000, red: 1.0000, green: 1.0000, blue: 1.0000, colorSpace: ColorSpace.sRGB)
flutter: APIWidget: Text color from string: rgba(255, 255, 255, 1) -> Color(alpha: 1.0000, red: 1.0000, green: 1.0000, blue: 1.0000, colorSpace: ColorSpace.sRGB)
flutter: APIWidget: Raw fontSize value: 96px (String)
flutter: APIWidget: Parsed font size: 96.0
flutter: APIWidget: Scaled font size: 10.444444444444445 (scale factor: 0.1087962962962963)
flutter: APIWidget: Font weight: bold -> FontWeight.w700
flutter: APIWidget: Text alignment: center -> TextAlign.center
flutter: APIWidget: Creating text widget with content: "42.35" at size 858.75 x 117.5
flutter: APIWidget: Added api.text widget with size 858.75x117.5
flutter: APIWidget: Built 1 content widgets
flutter: APIWidget: Controller update received for dce9c75d-bae9-4160-ac4d-0d274fe8dc96
flutter: APIWidget: Building content widgets for record index 0
flutter: APIWidget: Processing content item: {style: {color: #000000, fontSize: 32px, textAlign: center, fontFamily: Inter, sans-serif, fontWeight: normal, verticalAlign: middle, backgroundColor: transparent}, subtype: api.image, dataField: weather.icon, placeholder: API Data, placeholderUrl: http://localhost:5000/src/assets/weather-icons/{weather.icon}.gif}
flutter: APIWidget: Content item details - subtype: api.image, dataField: weather.icon, placeholderUrl: http://localhost:5000/src/assets/weather-icons/{weather.icon}.gif
flutter: APIWidget: Extracting field "weather.icon" (parts: weather, icon)
flutter: APIWidget: Found value for "weather.icon": 01d (String)
flutter: APIWidget: Widget dimensions - width: 500.0, height: 500.0
flutter: APIWidget: Building image widget with dataValue: 01d (String), placeholderUrl: http://localhost:5000/src/assets/weather-icons/{weather.icon}.gif
flutter: APIWidget: dataValue is String: 01d
flutter: APIWidget: URL validation check: 01d is invalid
flutter: APIWidget: Found field pattern {weather.icon} in placeholder URL
flutter: APIWidget: Using placeholder URL with substitution: http://localhost:5000/src/assets/weather-icons/{weather.icon}.gif -> http://localhost:5000/src/assets/weather-icons/01d.gif
flutter: APIWidget: Loading image from URL: http://localhost:5000/src/assets/weather-icons/01d.gif
flutter: APIWidget: Using direct streaming from remote server for image
flutter: APIWidget: Image type: Regular image
flutter: APIWidget: Image dimensions: 500.0 x 500.0
flutter: APIWidget: Rebuilt content for record 0 after sync
flutter: APIWidget: Rebuilt content for record 0 after sync
flutter: APIWidget: Added api.image widget with size 500.0x500.0
flutter: APIWidget: Built 1 content widgets
flutter: APIWidget: Rebuilt content for record 0 after sync
flutter: APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
flutter: APIWidget: Dimensions - width: 876.25, height: 387.5
flutter: APIWidget: Displaying 1 content widgets
flutter: APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
flutter: APIWidget: Dimensions - width: 858.75, height: 117.5
flutter: APIWidget: Displaying 1 content widgets
flutter: APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
flutter: APIWidget: Dimensions - width: 500.0, height: 500.0
flutter: APIWidget: Displaying 1 content widgets
flutter: LoggingService: Processing queue - Activities: 0, Proof of Play: 1
flutter: LoggingService: Processing 1 proof of play logs
flutter: LoggingService: Sending proof of play log to Supabase: ProofOfPlay(id: null, screenId: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaignId: 3cc80b6d-18d5-446a-b785-979c950c4d92, slideId: 80b479a8-d300-46ae-97d0-59653e52d73f, mediaId: null, logDatetime: 2025-05-31 11:49:10.850277Z)
flutter: [SupabaseService] Logging proof of play: ProofOfPlay(id: null, screenId: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaignId: 3cc80b6d-18d5-446a-b785-979c950c4d92, slideId: 80b479a8-d300-46ae-97d0-59653e52d73f, mediaId: null, logDatetime: 2025-05-31 11:49:10.850277Z)
flutter: [SupabaseService] Setting media_id to null for empty value
flutter: [SupabaseService] Proof of play data: {screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, log_datetime: 2025-05-31T11:49:10.850277Z, campaign_id: 3cc80b6d-18d5-446a-b785-979c950c4d92, slide_id: 80b479a8-d300-46ae-97d0-59653e52d73f, media_id: null}
flutter: [SupabaseService] Proof of play log response: [{id: 996, screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaign_id: 3cc80b6d-18d5-446a-b785-979c950c4d92, slide_id: 80b479a8-d300-46ae-97d0-59653e52d73f, media_id: null, log_datetime: 2025-05-31T11:49:10.850277+00:00}]
flutter: LoggingService: Proof of play log result: true
flutter: Safety timer: disposing old content widget: d696c03d-c48b-420e-9857-917a226024c4
flutter: ====================================================
flutter: Disposing content widget for schedule item: d696c03d-c48b-420e-9857-917a226024c4
flutter: Current active schedule item ID: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: Current schedule item ID: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: Content widgets in memory: d696c03d-c48b-420e-9857-917a226024c4, 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: Content widget disposed for schedule item: d696c03d-c48b-420e-9857-917a226024c4
flutter: Remaining widgets in memory: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: ====================================================
flutter: LoggingService: Processing queue - Activities: 0, Proof of Play: 0
flutter: PlatformVideoWidget: Desktop video completed
flutter: PlaylistWidget: end of playlist, calling onComplete
flutter: ====================================================
flutter: Content completed, moving to next item
flutter: Completed widget details:
flutter: Name: Weather
flutter: ID: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: Type: SlideShow
flutter: ====================================================
flutter: Checking campaign triggers before moving to next schedule item
flutter: === CAMPAIGN TRIGGER CHECK START ===
flutter: Current campaign index: 1
flutter: Total campaigns: 2
flutter: Checking next campaign in sequence: 0: General Campaign (trigger_type: 1)
flutter: Default loop campaign: General Campaign - displaying
flutter: Campaign General Campaign meets trigger conditions!
flutter: Moving from campaign 1 to 0
flutter: New campaign: General Campaign
flutter: === CAMPAIGN TRIGGER CHECK END ===
flutter: Finished all schedule items for campaign General Campaign
flutter: Moving to next campaign...
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Weather Campaign (trigger_type: 3)
flutter: Using Linux-specific location service
flutter: ApiDataController: GLOBAL timer expired (no videos), advancing to next record
flutter: ApiDataController: All records displayed (1/1), disposing slide and advancing to next schedule item
flutter: ====================================================
flutter: Content completed, moving to next item
flutter: Completed widget details:
flutter: Name: Weather
flutter: ID: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: Type: SlideShow
flutter: ====================================================
flutter: Checking campaign triggers before moving to next schedule item
flutter: === CAMPAIGN TRIGGER CHECK START ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking next campaign in sequence: 1: Weather Campaign (trigger_type: 3)
flutter: Using Linux-specific location service
flutter: Location obtained from IP: 21.1959, 72.8302
flutter: Fetching weather data from: https://api.openweathermap.org/data/2.5/weather?lat=21.1959&lon=72.8302&appid=29b1d3623a51675e8ed3c1d3fa13c0f0&units=metric
flutter: Location obtained from IP: 21.1959, 72.8302
flutter: Fetching weather data from: https://api.openweathermap.org/data/2.5/weather?lat=21.1959&lon=72.8302&appid=29b1d3623a51675e8ed3c1d3fa13c0f0&units=metric
flutter: Weather API response: 31.96°C
flutter: Weather check: campaign=Weather Campaign, temp=31.96, range=20.0-40.0, inRange=true
flutter: Campaign Weather Campaign meets trigger conditions!
flutter: Moving from campaign 0 to 1
flutter: New campaign: Weather Campaign
flutter: === CAMPAIGN TRIGGER CHECK END ===
flutter: Finished all schedule items for campaign Weather Campaign
flutter: Moving to next campaign...
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 1
flutter: Total campaigns: 2
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Default loop campaign: General Campaign - displaying
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: ====================================================
flutter: Playing next schedule item: BetterWithPepsi.mp4
flutter: ID: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: ====================================================
flutter: ====================================================
flutter: Starting transition to next content: BetterWithPepsi.mp4
flutter: Current content: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: Next content: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: ====================================================
flutter: ====================================================
flutter: Current widget that has finished playing:
flutter: Name: BetterWithPepsi.mp4
flutter: ID: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: Type: SimpleMedia
flutter: ====================================================
flutter: ====================================================
flutter: Creating SimpleMediaWidget for BetterWithPepsi.mp4
flutter: ID: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: File Path: /home/<USER>/Documents/signage/content/BetterWithPepsi.mp4
flutter: Is Image: false, Is Video: true
flutter: ====================================================
flutter: Storing content widget for schedule item: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: LoggingService: Logged simple media display: 5c1fa150-ec88-4b65-8693-003c728939e5
flutter: LoggingService: Processing queue - Activities: 0, Proof of Play: 1
flutter: LoggingService: Processing 1 proof of play logs
flutter: LoggingService: Sending proof of play log to Supabase: ProofOfPlay(id: null, screenId: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: null, mediaId: 5c1fa150-ec88-4b65-8693-003c728939e5, logDatetime: 2025-05-31 11:49:27.404359Z)
flutter: [SupabaseService] Logging proof of play: ProofOfPlay(id: null, screenId: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: null, mediaId: 5c1fa150-ec88-4b65-8693-003c728939e5, logDatetime: 2025-05-31 11:49:27.404359Z)
flutter: [SupabaseService] Proof of play data: {screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, log_datetime: 2025-05-31T11:49:27.404359Z, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: null, media_id: 5c1fa150-ec88-4b65-8693-003c728939e5}
flutter: Starting transition animation
flutter: Weather API response: 31.96°C
flutter: Weather check: campaign=Weather Campaign, temp=31.96, range=20.0-40.0, inRange=true
flutter: Campaign Weather Campaign meets trigger conditions!
flutter: Loaded 1 schedule items for campaign Weather Campaign
flutter:   [0] Weather
flutter: === MOVED TO CAMPAIGN Weather Campaign ===
flutter: ====================================================
flutter: Playing next schedule item: Weather
flutter: ID: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: ====================================================
flutter: ====================================================
flutter: Starting transition to next content: Weather
flutter: Current content: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: Next content: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: ====================================================
flutter: ====================================================
flutter: Current widget that has finished playing:
flutter: Name: Weather
flutter: ID: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: Type: SlideShow
flutter: ====================================================
flutter: ====================================================
flutter: Creating SlideShowWidget for Weather
flutter: ID: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: ====================================================
flutter: Storing content widget for schedule item: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: LoggingService: Logged slide display: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: Starting transition animation
flutter: Displaying content widget for schedule item: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: SlideShowWidget: initState for Weather
flutter: SlideShowWidget: Building content with design dimensions: 1920.0 x 1080.0
flutter: SlideShowWidget: Actual screen dimensions: 1920.0 x 1080.0
flutter: SlideShowWidget: Original playlist position: (0.0, 0.0), size: 1920.0 x 1080.0, zIndex: 1
flutter: SlideShowWidget: Expected scaled position: (0.0, 0.0), size: 1920.0 x 1080.0
flutter: SlideShowWidget: Original API position: (809.828125, 214.828125), size: 876.25 x 387.5, zIndex: 3
flutter: SlideShowWidget: Creating GLOBAL API data controller for all APIWidgets in this slide
flutter: ApiDataController: Created with apiUrl: https://api.openweathermap.org/data/2.5/weather?lat=17&lon=17&appid=29b1d3623a51675e8ed3c1d3fa13c0f0&units=metric, apiDataPreviewDuration: 0
flutter: ApiDataController: Fetching data from https://api.openweathermap.org/data/2.5/weather?lat=17&lon=17&appid=29b1d3623a51675e8ed3c1d3fa13c0f0&units=metric
flutter: ApiDataController: Setting up refresh timer for every 5 minutes
flutter: SlideShowWidget: GLOBAL API data controller created with apiDataPreviewDuration: 0
flutter: SlideShowWidget: Expected scaled API position: (809.828125, 214.828125), size: 876.25 x 387.5
flutter: SlideShowWidget: Original API position: (824.15625, 634.328125), size: 858.75 x 117.5, zIndex: 4
flutter: SlideShowWidget: Expected scaled API position: (824.15625, 634.328125), size: 858.75 x 117.5
flutter: SlideShowWidget: Original API position: (263.32916259765625, 258.32509358723956), size: 500.0 x 500.0, zIndex: 5
flutter: SlideShowWidget: Expected scaled API position: (263.32916259765625, 258.32509358723956), size: 500.0 x 500.0
flutter: SlideShowWidget: Player screen size: 1920.0x1080.0
flutter: SlideShowWidget: Design size: 1920.0 x 1080.0
flutter: SlideShowWidget: Scaling factors: horizontal=1.0, vertical=1.0
flutter: SlideShowWidget: Building scaled widgets with:
flutter:   - Design dimensions: 1920.0 x 1080.0
flutter:   - Screen dimensions: 1920.0 x 1080.0
flutter:   - Scale factors: 1.0 x 1.0
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=0.0, top=0.0, width=1920.0, height=1080.0
flutter:   - Scaled: left=0.0, top=0.0, width=1920.0, height=1080.0
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=809.828125, top=214.828125, width=876.25, height=387.5
flutter:   - Scaled: left=809.828125, top=214.828125, width=876.25, height=387.5
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=824.15625, top=634.328125, width=858.75, height=117.5
flutter:   - Scaled: left=824.15625, top=634.328125, width=858.75, height=117.5
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=263.32916259765625, top=258.32509358723956, width=500.0, height=500.0
flutter:   - Scaled: left=263.32916259765625, top=258.32509358723956, width=500.0, height=500.0
flutter: PlaylistWidget: Building with dimensions 1920.0 x 1080.0
flutter: APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
flutter: APIWidget: Dimensions - width: 500.0, height: 500.0
flutter: APIWidget: Displaying 1 content widgets
flutter: APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
flutter: APIWidget: Dimensions - width: 876.25, height: 387.5
flutter: APIWidget: Displaying 1 content widgets
flutter: APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
flutter: APIWidget: Dimensions - width: 858.75, height: 117.5
flutter: APIWidget: Displaying 1 content widgets
flutter: Displaying content widget for schedule item: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: SlideShowWidget: Player screen size: 1920.0x1080.0
flutter: SlideShowWidget: Design size: 1920.0 x 1080.0
flutter: SlideShowWidget: Scaling factors: horizontal=1.0, vertical=1.0
flutter: SlideShowWidget: Building scaled widgets with:
flutter:   - Design dimensions: 1920.0 x 1080.0
flutter:   - Screen dimensions: 1920.0 x 1080.0
flutter:   - Scale factors: 1.0 x 1.0
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=0.0, top=0.0, width=1920.0, height=1080.0
flutter:   - Scaled: left=0.0, top=0.0, width=1920.0, height=1080.0
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=809.828125, top=214.828125, width=876.25, height=387.5
flutter:   - Scaled: left=809.828125, top=214.828125, width=876.25, height=387.5
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=824.15625, top=634.328125, width=858.75, height=117.5
flutter:   - Scaled: left=824.15625, top=634.328125, width=858.75, height=117.5
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=263.32916259765625, top=258.32509358723956, width=500.0, height=500.0
flutter:   - Scaled: left=263.32916259765625, top=258.32509358723956, width=500.0, height=500.0
flutter: PlaylistWidget: initState for 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: PlaylistWidget: loading item weather-cloud.mp4 at index 0
flutter: APIWidget: initState for 5e11d095-702b-457c-bcec-db2adc7e5e91
flutter: APIWidget: Building widget with state - isLoading: true, hasError: false, isInitialized: false, contentWidgets: 0
flutter: APIWidget: Dimensions - width: 876.25, height: 387.5
flutter: APIWidget: Showing loading state
flutter: APIWidget: initState for 27a76786-c68e-4be7-9857-826c3a968cc4
flutter: APIWidget: Building widget with state - isLoading: true, hasError: false, isInitialized: false, contentWidgets: 0
flutter: APIWidget: Dimensions - width: 858.75, height: 117.5
flutter: APIWidget: Showing loading state
flutter: APIWidget: initState for dce9c75d-bae9-4160-ac4d-0d274fe8dc96
flutter: APIWidget: Building widget with state - isLoading: true, hasError: false, isInitialized: false, contentWidgets: 0
flutter: APIWidget: Dimensions - width: 500.0, height: 500.0
flutter: APIWidget: Showing loading state
flutter: [SupabaseService] Proof of play log response: [{id: 997, screen_id: c26fd436-f692-4bc1-853d-ee9ee478b94f, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: null, media_id: 5c1fa150-ec88-4b65-8693-003c728939e5, log_datetime: 2025-05-31T11:49:27.404359+00:00}]
flutter: LoggingService: Proof of play log result: true
flutter: Displaying content widget for schedule item: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: PlaylistWidget: Building with dimensions 1920.0 x 1080.0
flutter: Displaying content widget for schedule item: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: ApiDataController: API Response received with status 200
flutter: ApiDataController: Received 1 records from API
flutter: ApiDataController: API data fetched successfully, waiting for all APIWidgets to initialize
flutter: APIWidget: Controller update received for 5e11d095-702b-457c-bcec-db2adc7e5e91
flutter: APIWidget: Building content widgets for record index 0
flutter: APIWidget: Processing content item: {style: {color: rgba(255, 255, 255, 1), fontSize: 120px, textAlign: center, fontFamily: Helvetica, sans-serif, fontWeight: bold, verticalAlign: middle, backgroundColor: transparent}, subtype: api.text, dataField: weather.description, apiMapping: {weather.description: true}, placeholder: API Data, currentIndex: 0, placeholderUrl: }
flutter: APIWidget: Content item details - subtype: api.text, dataField: weather.description, placeholderUrl: 
flutter: APIWidget: Extracting field "weather.description" (parts: weather, description)
flutter: APIWidget: Found value for "weather.description": clear sky (String)
flutter: APIWidget: Widget dimensions - width: 876.25, height: 387.5
flutter: APIWidget: Building text widget with dataValue: clear sky (String)
flutter: APIWidget: Content config: {style: {color: rgba(255, 255, 255, 1), fontSize: 120px, textAlign: center, fontFamily: Helvetica, sans-serif, fontWeight: bold, verticalAlign: middle, backgroundColor: transparent}, subtype: api.text, dataField: weather.description, apiMapping: {weather.description: true}, placeholder: API Data, currentIndex: 0, placeholderUrl: }
media_kit: VideoOutput: video_output_new: 129915520187424
Cannot load libcuda.so.1
media_kit: VideoOutput: Using H/W rendering.
media_kit: VideoOutput: video_output_dispose: 129915519465232
media_kit: VideoOutput: video_output_dispose: 129915520187424
media_kit: VideoOutput: video_output_new: 129915525958560
Cannot load libcuda.so.1
media_kit: VideoOutput: Using H/W rendering.
media_kit: TextureGL: Resize: (1920, 1080)
flutter: APIWidget: Text content: "clear sky"
flutter: APIWidget: Text style: {color: rgba(255, 255, 255, 1), fontSize: 120px, textAlign: center, fontFamily: Helvetica, sans-serif, fontWeight: bold, verticalAlign: middle, backgroundColor: transparent}
flutter: APIWidget: Parsing color: rgba(255, 255, 255, 1)
flutter: APIWidget: Parsed rgba color: rgba(255, 255, 255, 1) -> Color(alpha: 1.0000, red: 1.0000, green: 1.0000, blue: 1.0000, colorSpace: ColorSpace.sRGB)
flutter: APIWidget: Text color from string: rgba(255, 255, 255, 1) -> Color(alpha: 1.0000, red: 1.0000, green: 1.0000, blue: 1.0000, colorSpace: ColorSpace.sRGB)
flutter: APIWidget: Raw fontSize value: 120px (String)
flutter: APIWidget: Parsed font size: 120.0
flutter: APIWidget: Scaled font size: 43.05555555555556 (scale factor: 0.3587962962962963)
flutter: APIWidget: Font weight: bold -> FontWeight.w700
flutter: APIWidget: Text alignment: center -> TextAlign.center
flutter: APIWidget: Creating text widget with content: "clear sky" at size 876.25 x 387.5
flutter: APIWidget: Added api.text widget with size 876.25x387.5
flutter: APIWidget: Built 1 content widgets
flutter: APIWidget: Controller update received for 27a76786-c68e-4be7-9857-826c3a968cc4
flutter: APIWidget: Building content widgets for record index 0
flutter: APIWidget: Processing content item: {style: {color: rgba(255, 255, 255, 1), fontSize: 96px, textAlign: center, fontFamily: Helvetica, sans-serif, fontWeight: bold, verticalAlign: middle, backgroundColor: transparent}, subtype: api.text, dataField: main.temp, apiMapping: {main.temp: true}, placeholder: API Data, currentIndex: 0, placeholderUrl: }
flutter: APIWidget: Content item details - subtype: api.text, dataField: main.temp, placeholderUrl: 
flutter: APIWidget: Extracting field "main.temp" (parts: main, temp)
flutter: APIWidget: Found value for "main.temp": 42.35 (double)
flutter: APIWidget: Widget dimensions - width: 858.75, height: 117.5
flutter: APIWidget: Building text widget with dataValue: 42.35 (double)
flutter: APIWidget: Content config: {style: {color: rgba(255, 255, 255, 1), fontSize: 96px, textAlign: center, fontFamily: Helvetica, sans-serif, fontWeight: bold, verticalAlign: middle, backgroundColor: transparent}, subtype: api.text, dataField: main.temp, apiMapping: {main.temp: true}, placeholder: API Data, currentIndex: 0, placeholderUrl: }
flutter: APIWidget: Text content: "42.35"
flutter: APIWidget: Text style: {color: rgba(255, 255, 255, 1), fontSize: 96px, textAlign: center, fontFamily: Helvetica, sans-serif, fontWeight: bold, verticalAlign: middle, backgroundColor: transparent}
flutter: APIWidget: Parsing color: rgba(255, 255, 255, 1)
flutter: APIWidget: Parsed rgba color: rgba(255, 255, 255, 1) -> Color(alpha: 1.0000, red: 1.0000, green: 1.0000, blue: 1.0000, colorSpace: ColorSpace.sRGB)
flutter: APIWidget: Text color from string: rgba(255, 255, 255, 1) -> Color(alpha: 1.0000, red: 1.0000, green: 1.0000, blue: 1.0000, colorSpace: ColorSpace.sRGB)
flutter: APIWidget: Raw fontSize value: 96px (String)
flutter: APIWidget: Parsed font size: 96.0
flutter: APIWidget: Scaled font size: 10.444444444444445 (scale factor: 0.1087962962962963)
flutter: APIWidget: Font weight: bold -> FontWeight.w700
flutter: APIWidget: Text alignment: center -> TextAlign.center
flutter: APIWidget: Creating text widget with content: "42.35" at size 858.75 x 117.5
flutter: APIWidget: Added api.text widget with size 858.75x117.5
flutter: APIWidget: Built 1 content widgets
flutter: APIWidget: Controller update received for dce9c75d-bae9-4160-ac4d-0d274fe8dc96
flutter: APIWidget: Building content widgets for record index 0
flutter: APIWidget: Processing content item: {style: {color: #000000, fontSize: 32px, textAlign: center, fontFamily: Inter, sans-serif, fontWeight: normal, verticalAlign: middle, backgroundColor: transparent}, subtype: api.image, dataField: weather.icon, placeholder: API Data, placeholderUrl: http://localhost:5000/src/assets/weather-icons/{weather.icon}.gif}
flutter: APIWidget: Content item details - subtype: api.image, dataField: weather.icon, placeholderUrl: http://localhost:5000/src/assets/weather-icons/{weather.icon}.gif
flutter: APIWidget: Extracting field "weather.icon" (parts: weather, icon)
flutter: APIWidget: Found value for "weather.icon": 01d (String)
flutter: APIWidget: Widget dimensions - width: 500.0, height: 500.0
flutter: APIWidget: Building image widget with dataValue: 01d (String), placeholderUrl: http://localhost:5000/src/assets/weather-icons/{weather.icon}.gif
flutter: APIWidget: dataValue is String: 01d
flutter: APIWidget: URL validation check: 01d is invalid
flutter: APIWidget: Found field pattern {weather.icon} in placeholder URL
flutter: APIWidget: Using placeholder URL with substitution: http://localhost:5000/src/assets/weather-icons/{weather.icon}.gif -> http://localhost:5000/src/assets/weather-icons/01d.gif
flutter: APIWidget: Loading image from URL: http://localhost:5000/src/assets/weather-icons/01d.gif
flutter: APIWidget: Using direct streaming from remote server for image
flutter: APIWidget: Image type: Regular image
flutter: APIWidget: Image dimensions: 500.0 x 500.0
flutter: APIWidget: Rebuilt content for record 0 after sync
flutter: APIWidget: Rebuilt content for record 0 after sync
flutter: APIWidget: Added api.image widget with size 500.0x500.0
flutter: APIWidget: Built 1 content widgets
flutter: APIWidget: Rebuilt content for record 0 after sync
flutter: APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
flutter: APIWidget: Dimensions - width: 876.25, height: 387.5
flutter: APIWidget: Displaying 1 content widgets
flutter: APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
flutter: APIWidget: Dimensions - width: 858.75, height: 117.5
flutter: APIWidget: Displaying 1 content widgets
flutter: APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
flutter: APIWidget: Dimensions - width: 500.0, height: 500.0
flutter: APIWidget: Displaying 1 content widgets
flutter: ====================================================
flutter: Completing transition
flutter: Current content ID: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: Next content ID: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: ====================================================
flutter: Displaying content widget for schedule item: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: SlideShowWidget: Player screen size: 1920.0x1080.0
flutter: SlideShowWidget: Design size: 1920.0 x 1080.0
flutter: SlideShowWidget: Scaling factors: horizontal=1.0, vertical=1.0
flutter: SlideShowWidget: Building scaled widgets with:
flutter:   - Design dimensions: 1920.0 x 1080.0
flutter:   - Screen dimensions: 1920.0 x 1080.0
flutter:   - Scale factors: 1.0 x 1.0
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=0.0, top=0.0, width=1920.0, height=1080.0
flutter:   - Scaled: left=0.0, top=0.0, width=1920.0, height=1080.0
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=809.828125, top=214.828125, width=876.25, height=387.5
flutter:   - Scaled: left=809.828125, top=214.828125, width=876.25, height=387.5
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=824.15625, top=634.328125, width=858.75, height=117.5
flutter:   - Scaled: left=824.15625, top=634.328125, width=858.75, height=117.5
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=263.32916259765625, top=258.32509358723956, width=500.0, height=500.0
flutter:   - Scaled: left=263.32916259765625, top=258.32509358723956, width=500.0, height=500.0
flutter: PlaylistWidget: Building with dimensions 1920.0 x 1080.0
flutter: APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
flutter: APIWidget: Dimensions - width: 876.25, height: 387.5
flutter: APIWidget: Displaying 1 content widgets
flutter: APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
flutter: APIWidget: Dimensions - width: 858.75, height: 117.5
flutter: APIWidget: Displaying 1 content widgets
flutter: APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
flutter: APIWidget: Dimensions - width: 500.0, height: 500.0
flutter: APIWidget: Displaying 1 content widgets
flutter: PlatformVideoWidget: Disposing video player
flutter: PlaylistWidget: dispose for 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: APIWidget: dispose for 5e11d095-702b-457c-bcec-db2adc7e5e91
flutter: APIWidget: No video controllers to clean up
flutter: APIWidget: All resources cleaned up
flutter: APIWidget: dispose for 27a76786-c68e-4be7-9857-826c3a968cc4
flutter: APIWidget: No video controllers to clean up
flutter: APIWidget: All resources cleaned up
flutter: APIWidget: dispose for dce9c75d-bae9-4160-ac4d-0d274fe8dc96
flutter: APIWidget: No video controllers to clean up
flutter: APIWidget: All resources cleaned up
flutter: SlideShowWidget: dispose for Weather
flutter: SlideShowWidget: Disposing API data controller
flutter: ApiDataController: Disposing
flutter: ====================================================
flutter: Completing transition
flutter: Current content ID: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: Next content ID: null
flutter: ====================================================
flutter: WARNING: No next content widget or ID available for transition
flutter: First item in schedule, but no next content widget available
flutter: This might be due to a crash or skipping the first item
flutter: Setting schedule index from 0 to 0
flutter: Schedule item: Weather
flutter: ====================================================
flutter: Forcing playback of first schedule item: Weather
flutter: ID: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: ====================================================
flutter: ====================================================
flutter: Current widget that has finished playing:
flutter: Name: Weather
flutter: ID: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: Type: SlideShow
flutter: ====================================================
flutter: ====================================================
flutter: Creating SlideShowWidget for Weather
flutter: ID: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: ====================================================
flutter: Storing content widget for schedule item: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: LoggingService: Logged slide display: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: VideoOutput.Resize
flutter: {handle: 129915520187424, id: 105105488447888, rect: {left: 0, top: 0, width: 1, height: 1}}
flutter: NativeVideoController: Texture ID: 105105488447888
flutter: Displaying content widget for schedule item: 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: SlideShowWidget: initState for Weather
flutter: SlideShowWidget: Building content with design dimensions: 1920.0 x 1080.0
flutter: SlideShowWidget: Actual screen dimensions: 1920.0 x 1080.0
flutter: SlideShowWidget: Original playlist position: (0.0, 0.0), size: 1920.0 x 1080.0, zIndex: 1
flutter: SlideShowWidget: Expected scaled position: (0.0, 0.0), size: 1920.0 x 1080.0
flutter: SlideShowWidget: Original API position: (809.828125, 214.828125), size: 876.25 x 387.5, zIndex: 3
flutter: SlideShowWidget: Creating GLOBAL API data controller for all APIWidgets in this slide
flutter: ApiDataController: Created with apiUrl: https://api.openweathermap.org/data/2.5/weather?lat=17&lon=17&appid=29b1d3623a51675e8ed3c1d3fa13c0f0&units=metric, apiDataPreviewDuration: 0
flutter: ApiDataController: Fetching data from https://api.openweathermap.org/data/2.5/weather?lat=17&lon=17&appid=29b1d3623a51675e8ed3c1d3fa13c0f0&units=metric
flutter: ApiDataController: Setting up refresh timer for every 5 minutes
flutter: SlideShowWidget: GLOBAL API data controller created with apiDataPreviewDuration: 0
flutter: SlideShowWidget: Expected scaled API position: (809.828125, 214.828125), size: 876.25 x 387.5
flutter: SlideShowWidget: Original API position: (824.15625, 634.328125), size: 858.75 x 117.5, zIndex: 4
flutter: SlideShowWidget: Expected scaled API position: (824.15625, 634.328125), size: 858.75 x 117.5
flutter: SlideShowWidget: Original API position: (263.32916259765625, 258.32509358723956), size: 500.0 x 500.0, zIndex: 5
flutter: SlideShowWidget: Expected scaled API position: (263.32916259765625, 258.32509358723956), size: 500.0 x 500.0
flutter: PlatformVideoWidget: Disposing video player
flutter: PlaylistWidget: dispose for 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: APIWidget: dispose for 5e11d095-702b-457c-bcec-db2adc7e5e91
flutter: APIWidget: No video controllers to clean up
flutter: APIWidget: All resources cleaned up
flutter: APIWidget: dispose for 27a76786-c68e-4be7-9857-826c3a968cc4
flutter: APIWidget: No video controllers to clean up
flutter: APIWidget: All resources cleaned up
flutter: APIWidget: dispose for dce9c75d-bae9-4160-ac4d-0d274fe8dc96
flutter: APIWidget: No video controllers to clean up
flutter: APIWidget: All resources cleaned up
flutter: SlideShowWidget: dispose for Weather
flutter: SlideShowWidget: Disposing API data controller
flutter: ApiDataController: Disposing
flutter: SlideShowWidget: Player screen size: 1920.0x1080.0
flutter: SlideShowWidget: Design size: 1920.0 x 1080.0
flutter: SlideShowWidget: Scaling factors: horizontal=1.0, vertical=1.0
flutter: SlideShowWidget: Building scaled widgets with:
flutter:   - Design dimensions: 1920.0 x 1080.0
flutter:   - Screen dimensions: 1920.0 x 1080.0
flutter:   - Scale factors: 1.0 x 1.0
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=0.0, top=0.0, width=1920.0, height=1080.0
flutter:   - Scaled: left=0.0, top=0.0, width=1920.0, height=1080.0
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=809.828125, top=214.828125, width=876.25, height=387.5
flutter:   - Scaled: left=809.828125, top=214.828125, width=876.25, height=387.5
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=824.15625, top=634.328125, width=858.75, height=117.5
flutter:   - Scaled: left=824.15625, top=634.328125, width=858.75, height=117.5
flutter: SlideShowWidget: Scaling widget:
flutter:   - Original: left=263.32916259765625, top=258.32509358723956, width=500.0, height=500.0
flutter:   - Scaled: left=263.32916259765625, top=258.32509358723956, width=500.0, height=500.0
flutter: PlaylistWidget: initState for 80b479a8-d300-46ae-97d0-59653e52d73f
flutter: PlaylistWidget: loading item weather-cloud.mp4 at index 0
flutter: APIWidget: initState for 5e11d095-702b-457c-bcec-db2adc7e5e91
flutter: APIWidget: Building widget with state - isLoading: true, hasError: false, isInitialized: false, contentWidgets: 0
flutter: APIWidget: Dimensions - width: 876.25, height: 387.5
flutter: APIWidget: Showing loading state
flutter: APIWidget: initState for 27a76786-c68e-4be7-9857-826c3a968cc4
flutter: APIWidget: Building widget with state - isLoading: true, hasError: false, isInitialized: false, contentWidgets: 0
flutter: APIWidget: Dimensions - width: 858.75, height: 117.5
flutter: APIWidget: Showing loading state
flutter: APIWidget: initState for dce9c75d-bae9-4160-ac4d-0d274fe8dc96
flutter: APIWidget: Building widget with state - isLoading: true, hasError: false, isInitialized: false, contentWidgets: 0
flutter: APIWidget: Dimensions - width: 500.0, height: 500.0
flutter: APIWidget: Showing loading state
flutter: PlaylistWidget: Building with dimensions 1920.0 x 1080.0
flutter: ApiDataController: API Response received with status 200
flutter: ApiDataController: Received 1 records from API
flutter: ApiDataController: API data fetched successfully, waiting for all APIWidgets to initialize
flutter: APIWidget: Controller update received for 5e11d095-702b-457c-bcec-db2adc7e5e91
flutter: APIWidget: Building content widgets for record index 0
flutter: APIWidget: Processing content item: {style: {color: rgba(255, 255, 255, 1), fontSize: 120px, textAlign: center, fontFamily: Helvetica, sans-serif, fontWeight: bold, verticalAlign: middle, backgroundColor: transparent}, subtype: api.text, dataField: weather.description, apiMapping: {weather.description: true}, placeholder: API Data, currentIndex: 0, placeholderUrl: }
flutter: APIWidget: Content item details - subtype: api.text, dataField: weather.description, placeholderUrl: 
flutter: APIWidget: Extracting field "weather.description" (parts: weather, description)
flutter: APIWidget: Found value for "weather.description": clear sky (String)
flutter: APIWidget: Widget dimensions - width: 876.25, height: 387.5
flutter: APIWidget: Building text widget with dataValue: clear sky (String)
flutter: APIWidget: Content config: {style: {color: rgba(255, 255, 255, 1), fontSize: 120px, textAlign: center, fontFamily: Helvetica, sans-serif, fontWeight: bold, verticalAlign: middle, backgroundColor: transparent}, subtype: api.text, dataField: weather.description, apiMapping: {weather.description: true}, placeholder: API Data, currentIndex: 0, placeholderUrl: }
flutter: APIWidget: Text content: "clear sky"
flutter: APIWidget: Text style: {color: rgba(255, 255, 255, 1), fontSize: 120px, textAlign: center, fontFamily: Helvetica, sans-serif, fontWeight: bold, verticalAlign: middle, backgroundColor: transparent}
flutter: APIWidget: Parsing color: rgba(255, 255, 255, 1)
flutter: APIWidget: Parsed rgba color: rgba(255, 255, 255, 1) -> Color(alpha: 1.0000, red: 1.0000, green: 1.0000, blue: 1.0000, colorSpace: ColorSpace.sRGB)
flutter: APIWidget: Text color from string: rgba(255, 255, 255, 1) -> Color(alpha: 1.0000, red: 1.0000, green: 1.0000, blue: 1.0000, colorSpace: ColorSpace.sRGB)
flutter: APIWidget: Raw fontSize value: 120px (String)
flutter: APIWidget: Parsed font size: 120.0
flutter: APIWidget: Scaled font size: 43.05555555555556 (scale factor: 0.3587962962962963)
flutter: APIWidget: Font weight: bold -> FontWeight.w700
flutter: APIWidget: Text alignment: center -> TextAlign.center
flutter: APIWidget: Creating text widget with content: "clear sky" at size 876.25 x 387.5
flutter: APIWidget: Added api.text widget with size 876.25x387.5
flutter: APIWidget: Built 1 content widgets
flutter: APIWidget: Controller update received for 27a76786-c68e-4be7-9857-826c3a968cc4
flutter: APIWidget: Building content widgets for record index 0
flutter: APIWidget: Processing content item: {style: {color: rgba(255, 255, 255, 1), fontSize: 96px, textAlign: center, fontFamily: Helvetica, sans-serif, fontWeight: bold, verticalAlign: middle, backgroundColor: transparent}, subtype: api.text, dataField: main.temp, apiMapping: {main.temp: true}, placeholder: API Data, currentIndex: 0, placeholderUrl: }
flutter: APIWidget: Content item details - subtype: api.text, dataField: main.temp, placeholderUrl: 
flutter: APIWidget: Extracting field "main.temp" (parts: main, temp)
flutter: APIWidget: Found value for "main.temp": 42.35 (double)
flutter: APIWidget: Widget dimensions - width: 858.75, height: 117.5
flutter: APIWidget: Building text widget with dataValue: 42.35 (double)
flutter: APIWidget: Content config: {style: {color: rgba(255, 255, 255, 1), fontSize: 96px, textAlign: center, fontFamily: Helvetica, sans-serif, fontWeight: bold, verticalAlign: middle, backgroundColor: transparent}, subtype: api.text, dataField: main.temp, apiMapping: {main.temp: true}, placeholder: API Data, currentIndex: 0, placeholderUrl: }
flutter: APIWidget: Text content: "42.35"
flutter: APIWidget: Text style: {color: rgba(255, 255, 255, 1), fontSize: 96px, textAlign: center, fontFamily: Helvetica, sans-serif, fontWeight: bold, verticalAlign: middle, backgroundColor: transparent}
flutter: APIWidget: Parsing color: rgba(255, 255, 255, 1)
flutter: APIWidget: Parsed rgba color: rgba(255, 255, 255, 1) -> Color(alpha: 1.0000, red: 1.0000, green: 1.0000, blue: 1.0000, colorSpace: ColorSpace.sRGB)
flutter: APIWidget: Text color from string: rgba(255, 255, 255, 1) -> Color(alpha: 1.0000, red: 1.0000, green: 1.0000, blue: 1.0000, colorSpace: ColorSpace.sRGB)
flutter: APIWidget: Raw fontSize value: 96px (String)
flutter: APIWidget: Parsed font size: 96.0
flutter: APIWidget: Scaled font size: 10.444444444444445 (scale factor: 0.1087962962962963)
flutter: APIWidget: Font weight: bold -> FontWeight.w700
flutter: APIWidget: Text alignment: center -> TextAlign.center
flutter: APIWidget: Creating text widget with content: "42.35" at size 858.75 x 117.5
flutter: APIWidget: Added api.text widget with size 858.75x117.5
flutter: APIWidget: Built 1 content widgets
flutter: APIWidget: Controller update received for dce9c75d-bae9-4160-ac4d-0d274fe8dc96
flutter: APIWidget: Building content widgets for record index 0
flutter: APIWidget: Processing content item: {style: {color: #000000, fontSize: 32px, textAlign: center, fontFamily: Inter, sans-serif, fontWeight: normal, verticalAlign: middle, backgroundColor: transparent}, subtype: api.image, dataField: weather.icon, placeholder: API Data, placeholderUrl: http://localhost:5000/src/assets/weather-icons/{weather.icon}.gif}
flutter: APIWidget: Content item details - subtype: api.image, dataField: weather.icon, placeholderUrl: http://localhost:5000/src/assets/weather-icons/{weather.icon}.gif
flutter: APIWidget: Extracting field "weather.icon" (parts: weather, icon)
flutter: APIWidget: Found value for "weather.icon": 01d (String)
flutter: APIWidget: Widget dimensions - width: 500.0, height: 500.0
flutter: APIWidget: Building image widget with dataValue: 01d (String), placeholderUrl: http://localhost:5000/src/assets/weather-icons/{weather.icon}.gif
flutter: APIWidget: dataValue is String: 01d
flutter: APIWidget: URL validation check: 01d is invalid
flutter: APIWidget: Found field pattern {weather.icon} in placeholder URL
flutter: APIWidget: Using placeholder URL with substitution: http://localhost:5000/src/assets/weather-icons/{weather.icon}.gif -> http://localhost:5000/src/assets/weather-icons/01d.gif
flutter: APIWidget: Loading image from URL: http://localhost:5000/src/assets/weather-icons/01d.gif
flutter: APIWidget: Using direct streaming from remote server for image
flutter: APIWidget: Image type: Regular image
flutter: APIWidget: Image dimensions: 500.0 x 500.0
flutter: APIWidget: Rebuilt content for record 0 after sync
flutter: APIWidget: Rebuilt content for record 0 after sync
flutter: APIWidget: Added api.image widget with size 500.0x500.0
flutter: APIWidget: Built 1 content widgets
flutter: APIWidget: Rebuilt content for record 0 after sync
flutter: APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
flutter: APIWidget: Dimensions - width: 876.25, height: 387.5
flutter: APIWidget: Displaying 1 content widgets
flutter: APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
flutter: APIWidget: Dimensions - width: 858.75, height: 117.5
flutter: APIWidget: Displaying 1 content widgets
flutter: APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
flutter: APIWidget: Dimensions - width: 500.0, height: 500.0
flutter: APIWidget: Displaying 1 content widgets
flutter: VideoOutput.Resize
flutter: {handle: 129915525958560, id: 105105493451680, rect: {left: 0, top: 0, width: 1, height: 1}}
flutter: NativeVideoController: Texture ID: 105105493451680
flutter: ApiDataController: Checking if all widgets are ready - dataReady: true, videoWidgets: 0
flutter: SlideShowWidget: All APIWidgets are ready, starting global timer
flutter: ApiDataController: Starting global timer for all APIWidgets
flutter: ApiDataController: Starting GLOBAL display timer - videoWidgets: 0, apiDataPreviewDuration: 0
flutter: ApiDataController: Starting GLOBAL display timer for 15 seconds (no videos)
flutter: APIWidget: Controller update received for 5e11d095-702b-457c-bcec-db2adc7e5e91
flutter: APIWidget: Controller update received for 27a76786-c68e-4be7-9857-826c3a968cc4
flutter: APIWidget: Controller update received for dce9c75d-bae9-4160-ac4d-0d274fe8dc96
flutter: APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
flutter: APIWidget: Dimensions - width: 876.25, height: 387.5
flutter: APIWidget: Displaying 1 content widgets
flutter: APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
flutter: APIWidget: Dimensions - width: 858.75, height: 117.5
flutter: APIWidget: Displaying 1 content widgets
flutter: APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
flutter: APIWidget: Dimensions - width: 500.0, height: 500.0
flutter: APIWidget: Displaying 1 content widgets
flutter: VideoOutput.Resize
flutter: {handle: 129915525958560, id: 105105493451680, rect: {left: 0, top: 0, width: 1920, height: 1080}}
flutter: CursorManager: Showing cursor
flutter: CursorManager: Hiding cursor
flutter: AppExitService: Exiting app on platform: Linux
flutter: AppExitService: Exiting desktop app with exit(0)
Lost connection to device.