import 'dart:async';
import 'package:flutter/foundation.dart';

/// A timer for controlling content display duration
class ContentTimer {
  /// The duration of the timer in seconds
  final int durationInSeconds;

  /// Callback when the timer completes
  final VoidCallback onComplete;

  /// The timer instance
  Timer? _timer;

  /// Creates a ContentTimer
  ContentTimer({
    required this.durationInSeconds,
    required this.onComplete,
  });

  /// Start the timer
  void start() {
    // Cancel any existing timer
    _timer?.cancel();

    // Start a new timer
    _timer = Timer(Duration(seconds: durationInSeconds), () {
      onComplete();
    });
  }

  /// Cancel the timer
  void cancel() {
    _timer?.cancel();
    _timer = null;
  }

  /// Dispose the timer
  void dispose() {
    cancel();
  }
}
