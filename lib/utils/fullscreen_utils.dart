import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:signage/utils/platform_utils.dart';

class FullscreenUtils {
  /// Initialize fullscreen mode based on the platform
  static Future<void> initializeFullscreen(BuildContext context) async {
    // Allow all orientations (portrait and landscape)
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);

    // Hide system UI overlays for all platforms
    await SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.immersiveSticky,
      overlays: [], // Hide all overlays
    );

    // For Android, ensure the status bar and navigation bar are hidden
    if (PlatformUtils.isAndroid) {
      SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.light,
        systemNavigationBarContrastEnforced: false,
        systemStatusBarContrastEnforced: false,
      ));
    }

    // For desktop platforms, we rely on the native code modifications
    // The Linux window is set to fullscreen in my_application.cc
    // The Windows window will be set to fullscreen in win32_window.cpp
  }

  /// Ensure fullscreen mode is maintained (call this when app regains focus)
  static Future<void> ensureFullscreen() async {
    // Note: We don't reset orientation here to allow the device to maintain its current orientation

    // Hide system UI overlays
    await SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.immersiveSticky,
      overlays: [], // Hide all overlays
    );

    // For Android, ensure the status bar and navigation bar are hidden
    if (PlatformUtils.isAndroid) {
      SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.light,
        systemNavigationBarContrastEnforced: false,
        systemStatusBarContrastEnforced: false,
      ));
    }
  }
}
