
import 'package:flutter/material.dart';
import 'dart:math' as math;

class GaugeChart extends StatefulWidget {
  final double value;
  final double minValue;
  final double maxValue;
  final int segments;
  final Color needleColor;
  final double ringWidth;
  final Color textColor;
  final double labelFontSize;
  final double valueTextFontSize;
  final FontWeight valueTextFontWeight;
  final double paddingHorizontal;
  final double paddingVertical;
  final double width;
  final double height;
  final List<Color> segmentColors;
  final List<double>? segmentStops;
  final List<CustomSegmentLabel> customSegmentLabels;
  final String? currentValueText;
  final String? valueLabel;
  final Duration animationDuration;

  const GaugeChart({
    super.key,
    required this.value,
    this.minValue = 0,
    this.maxValue = 100,
    this.segments = 4,
    this.needleColor = Colors.blue,
    this.ringWidth = 20,
    this.textColor = Colors.black,
    this.labelFontSize = 14,
    this.valueTextFontSize = 24,
    this.valueTextFontWeight = FontWeight.bold,
    this.paddingHorizontal = 15,
    this.paddingVertical = 15,
    this.width = 300,
    this.height = 200,
    this.segmentColors = const [Colors.blue, Colors.green, Colors.orange, Colors.red],
    this.segmentStops,
    this.customSegmentLabels = const [],
    this.currentValueText = 'The current value is Low',
    this.valueLabel = '20',
    this.animationDuration = const Duration(milliseconds: 1000),
  });

  @override
  State<GaugeChart> createState() => _GaugeChartState();
}

class _GaugeChartState extends State<GaugeChart> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );
    
    _animation = Tween<double>(
      begin: widget.minValue,
      end: widget.value,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
  }

  @override
  void didUpdateWidget(GaugeChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _animation = Tween<double>(
        begin: _animation.value,
        end: widget.value,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutCubic,
      ));
      
      _animationController.forward(from: 0);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: widget.paddingVertical,
        horizontal: widget.paddingHorizontal,
      ),
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return CustomPaint(
            size: Size(widget.width, widget.height),
            painter: _GaugePainter(
              value: _animation.value,
              minValue: widget.minValue,
              maxValue: widget.maxValue,
              segments: widget.segments,
              needleColor: widget.needleColor,
              ringWidth: widget.ringWidth,
              textColor: widget.textColor,
              labelFontSize: widget.labelFontSize,
              valueTextFontSize: widget.valueTextFontSize,
              valueTextFontWeight: widget.valueTextFontWeight,
              segmentColors: widget.segmentColors,
              segmentStops: widget.segmentStops,
              customSegmentLabels: widget.customSegmentLabels,
              currentValueText: widget.currentValueText,
            ),
          );
        },
      ),
    );
  }
}

class _GaugePainter extends CustomPainter {
  final double value, minValue, maxValue, ringWidth, labelFontSize, valueTextFontSize;
  final int segments;
  final Color needleColor, textColor;
  final FontWeight valueTextFontWeight;
  final List<Color> segmentColors;
  final List<double>? segmentStops;
  final List<CustomSegmentLabel> customSegmentLabels;
  final String? currentValueText;

  _GaugePainter({
    required this.value,
    required this.minValue,
    required this.maxValue,
    required this.segments,
    required this.needleColor,
    required this.ringWidth,
    required this.textColor,
    required this.labelFontSize,
    required this.valueTextFontSize,
    required this.valueTextFontWeight,
    required this.segmentColors,
    required this.segmentStops,
    required this.customSegmentLabels,
    required this.currentValueText,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final cx = size.width / 2;
    final cy = size.height;
    final radius = math.min(size.width / 2, size.height) - ringWidth;

    final segmentBoundaries = _calculateSegmentBoundaries();

    final arcPaint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = ringWidth;

    for (int i = 0; i < segments; i++) {
      final startValue = segmentBoundaries[i];
      final endValue = segmentBoundaries[i + 1];
      final startAngle = _valueToRadians(startValue);
      final sweepAngle = _valueToRadians(endValue) - startAngle;

      arcPaint.color = segmentColors[i % segmentColors.length];
      canvas.drawArc(
        Rect.fromCircle(center: Offset(cx, cy), radius: radius),
        startAngle,
        sweepAngle,
        false,
        arcPaint,
      );

      if (i < customSegmentLabels.length) {
        final labelAngle = _valueToRadians((startValue + endValue) / 2);
        final labelRadius = radius + 8;
        final lx = cx + labelRadius * math.cos(labelAngle);
        final ly = cy + labelRadius * math.sin(labelAngle);

        canvas.save();
        canvas.translate(lx, ly);
        canvas.rotate(labelAngle + math.pi / 2);

        final tp = TextPainter(
          text: TextSpan(
            text: customSegmentLabels[i].text,
            style: TextStyle(
              fontSize: labelFontSize,
              fontWeight: FontWeight.bold,
              color: _parseColor(customSegmentLabels[i].color),
            ),
          ),
          textDirection: TextDirection.ltr,
        )..layout();
        tp.paint(canvas, Offset(-tp.width / 2, -tp.height / 2));
        canvas.restore();
      }
    }

    final needleAngleDeg = -180 + ((value - minValue) / (maxValue - minValue)) * 180;
    final needleAngle = needleAngleDeg * math.pi / 180;
    final needleLength = radius - 10;
    final needleEnd = Offset(
      cx + needleLength * math.cos(needleAngle),
      cy + needleLength * math.sin(needleAngle),
    );

    final path = Path()
      ..moveTo(cx, cy)
      ..lineTo(
        cx + 5 * math.cos(needleAngle - math.pi / 2),
        cy + 5 * math.sin(needleAngle - math.pi / 2),
      )
      ..lineTo(needleEnd.dx, needleEnd.dy)
      ..lineTo(
        cx + 5 * math.cos(needleAngle + math.pi / 2),
        cy + 5 * math.sin(needleAngle + math.pi / 2),
      )
      ..close();

    final needlePaint = Paint()
      ..color = needleColor
      ..style = PaintingStyle.fill;

    canvas.drawPath(path, needlePaint);
    canvas.drawCircle(Offset(cx, cy), 6, needlePaint);

    final valuePainter = TextPainter(
      text: TextSpan(
        text: value.toStringAsFixed(1),
        style: TextStyle(
          fontSize: valueTextFontSize,
          fontWeight: valueTextFontWeight,
          color: textColor,
        ),
      ),
      textDirection: TextDirection.ltr,
    )..layout();
    valuePainter.paint(canvas, Offset(cx - valuePainter.width / 2, cy - radius / 2));

    final labelText = _getCurrentLabel();
    final labelPainter = TextPainter(
      text: TextSpan(
        text: labelText,
        style: TextStyle(
          fontSize: labelFontSize,
          fontWeight: valueTextFontWeight,
          color: textColor,
        ),
      ),
      textDirection: TextDirection.ltr,
    )..layout();
    labelPainter.paint(canvas, Offset(cx - labelPainter.width / 2, cy + radius / 8));
  }

  List<double> _calculateSegmentBoundaries() {
    if (segmentStops != null && segmentStops!.length == segments - 1) {
      final stops = [...segmentStops!]..sort();
      return [minValue, ...stops, maxValue];
    } else {
      final step = (maxValue - minValue) / segments;
      return List.generate(segments + 1, (i) => minValue + i * step);
    }
  }

  double _valueToRadians(double value) {
    final normalized = (value - minValue) / (maxValue - minValue);
    // Change from starting at -90° (top) to starting at -180° (left)
    return (normalized * math.pi) - math.pi;
  }

  String _getCurrentLabel() {
    final boundaries = _calculateSegmentBoundaries();
    int index = 0;
    for (int i = 0; i < boundaries.length - 1; i++) {
      if (value >= boundaries[i] && value <= boundaries[i + 1]) {
        index = i;
        break;
      }
    }
    final label = (customSegmentLabels.length > index && customSegmentLabels[index].currentValueText != null)
        ? customSegmentLabels[index].currentValueText!
        : currentValueText ?? '';
    return label.replaceAll('{{value}}', value.toStringAsFixed(1));
  }

  Color _parseColor(String colorStr) {
    // Handle rgba format: rgba(255, 0, 0, 1)
    if (colorStr.startsWith('rgba(') && colorStr.endsWith(')')) {
      try {
        // Extract the rgba values
        final values = colorStr
            .substring(5, colorStr.length - 1)
            .split(',')
            .map((s) => s.trim())
            .toList();

        if (values.length == 4) {
          final r = int.parse(values[0]);
          final g = int.parse(values[1]);
          final b = int.parse(values[2]);
          final a = double.parse(values[3]);

          return Color.fromRGBO(r, g, b, a);
        }
      } catch (e) {
        return Colors.black; // Default on error
      }
    }
    
    // Handle rgb format: rgb(255, 0, 0)
    if (colorStr.startsWith('rgb(') && colorStr.endsWith(')')) {
      try {
        // Extract the rgb values
        final values = colorStr
            .substring(4, colorStr.length - 1)
            .split(',')
            .map((s) => s.trim())
            .toList();

        if (values.length == 3) {
          final r = int.parse(values[0]);
          final g = int.parse(values[1]);
          final b = int.parse(values[2]);

          return Color.fromRGBO(r, g, b, 1.0);
        }
      } catch (e) {
        return Colors.black; // Default on error
      }
    }
    
    // Handle hex format
    try {
      String hex = colorStr.replaceAll('#', '');
      if (hex.length == 6) hex = 'FF$hex';
      return Color(int.parse('0x$hex'));
    } catch (_) {
      return Colors.black;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}

class CustomSegmentLabel {
  final String text;
  final String position;
  final String color;
  final String? currentValueText;

  CustomSegmentLabel({
    required this.text,
    required this.position,
    required this.color,
    this.currentValueText,
  });
}
