import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:signage/core/models/settings.dart';
import 'package:signage/core/permissions/permission_handler.dart';
import 'package:signage/core/services/supabase_service.dart';
import 'package:signage/core/storage/storage_service.dart';
import 'package:signage/ui/screens/data_loading_screen.dart';
import 'package:signage/ui/screens/player_screen.dart';
import 'package:signage/ui/screens/registration_screen.dart';
import 'package:signage/utils/platform_utils.dart';
import 'package:signage/utils/fullscreen_utils.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> with WidgetsBindingObserver {
  String _appVersion = '';
  double _progress = 0.0;
  String _statusMessage = 'Initializing...';

  @override
  void initState() {
    super.initState();
    // Register this object as an observer for app lifecycle changes
    WidgetsBinding.instance.addObserver(this);
    initializeApp();
  }

  @override
  void dispose() {
    // Unregister this object as an observer
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // When app resumes from background, ensure fullscreen mode
    if (state == AppLifecycleState.resumed) {
      FullscreenUtils.ensureFullscreen();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Initialize fullscreen mode after the context is available
    FullscreenUtils.initializeFullscreen(context);
  }

  /// Build a permission instruction item widget
  Widget buildPermissionInstructionItem(String title, String instructions) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 4),
          Text(instructions),
        ],
      ),
    );
  }

  /// Show a dialog with detailed instructions for granting permissions
  Future<void> showPermissionInstructionsDialog() async {
    // Check status of special permissions
    final batteryOptStatus = await Permission.ignoreBatteryOptimizations.status;
    final storageStatus = await Permission.manageExternalStorage.status;
    final mediaLocationStatus = await Permission.accessMediaLocation.status;
    final alertWindowStatus = await Permission.systemAlertWindow.status;
    final notificationPolicyStatus = await Permission.accessNotificationPolicy.status;
    final locationStatus = await Permission.locationWhenInUse.status;

    // Build a list of permissions that need attention
    List<Widget> permissionInstructions = [];

    if (batteryOptStatus.isDenied || batteryOptStatus.isPermanentlyDenied) {
      permissionInstructions.add(buildPermissionInstructionItem(
        'Battery Optimization',
        PermissionHandler.getPermissionInstructions(Permission.ignoreBatteryOptimizations)
      ));
    }

    if (storageStatus.isDenied || storageStatus.isPermanentlyDenied) {
      permissionInstructions.add(buildPermissionInstructionItem(
        'Storage Management',
        PermissionHandler.getPermissionInstructions(Permission.manageExternalStorage)
      ));
    }

    if (mediaLocationStatus.isDenied || mediaLocationStatus.isPermanentlyDenied) {
      permissionInstructions.add(buildPermissionInstructionItem(
        'Media Location',
        PermissionHandler.getPermissionInstructions(Permission.accessMediaLocation)
      ));
    }

    if (alertWindowStatus.isDenied || alertWindowStatus.isPermanentlyDenied) {
      permissionInstructions.add(buildPermissionInstructionItem(
        'Display Over Other Apps',
        PermissionHandler.getPermissionInstructions(Permission.systemAlertWindow)
      ));
    }

    if (notificationPolicyStatus.isDenied || notificationPolicyStatus.isPermanentlyDenied) {
      permissionInstructions.add(buildPermissionInstructionItem(
        'Notification Policy Access',
        PermissionHandler.getPermissionInstructions(Permission.accessNotificationPolicy)
      ));
    }

    if (locationStatus.isDenied || locationStatus.isPermanentlyDenied) {
      permissionInstructions.add(buildPermissionInstructionItem(
        'Location',
        PermissionHandler.getPermissionInstructions(Permission.locationWhenInUse)
      ));
    }

    // We're removing the Background Location permission dialog as it's not required
    // and is causing the app to get stuck even when other permissions are granted

    // Only show the dialog if there are permissions that need attention
    if (permissionInstructions.isEmpty) {
      // If no permissions need attention, continue with app initialization
      if (mounted) {
        initializeApp();
      }
      return;
    }

    // Show dialog with instructions
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('Permission Instructions'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'The following permissions need to be granted manually from settings:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  ...permissionInstructions,
                ],
              ),
            ),
            actions: <Widget>[
              TextButton(
                child: const Text('Open Settings'),
                onPressed: () async {
                  Navigator.of(context).pop();
                  await PermissionHandler.openSettings();
                  // After returning from settings, check permissions again
                  if (mounted) {
                    initializeApp();
                  }
                },
              ),
              TextButton(
                child: const Text('Continue Anyway'),
                onPressed: () {
                  Navigator.of(context).pop();
                  // Continue with app initialization even if some permissions are not granted
                  if (mounted) {
                    initializeApp();
                  }
                },
              ),
              TextButton(
                child: const Text('Exit App'),
                onPressed: () {
                  // Exit the app
                  SystemNavigator.pop();
                },
              ),
            ],
          );
        },
      );
    }
  }

  Future<void> initializeApp() async {
    // Get app version
    final packageInfo = await PackageInfo.fromPlatform();
    if (!mounted) return;

    setState(() {
      _appVersion = packageInfo.version;
      _statusMessage = 'Checking platform...';
      _progress = 0.1;
    });

    // Detect platform
    final platform = PlatformUtils.platformName;
    if (!mounted) return;

    setState(() {
      _statusMessage = 'Detected platform: $platform';
      _progress = 0.2;
    });

    // Request permissions if on Android
    if (PlatformUtils.isAndroid) {
      if (!mounted) return;

      setState(() {
        _statusMessage = 'Requesting permissions...';
        _progress = 0.3;
      });

      final permissionsGranted = await PermissionHandler.requestPermissions();
      if (!mounted) return;

      if (!permissionsGranted) {
        setState(() {
          _statusMessage = 'Some permissions are not granted. Checking which ones...';
          _progress = 0.3;
        });

        // Check if location permission is granted, which is the most important one
        final locationGranted = await Permission.locationWhenInUse.isGranted;
        if (!mounted) return;

        if (locationGranted) {
          // If location is granted, we can continue even if other permissions are not
          setState(() {
            _statusMessage = 'Essential permissions granted, continuing...';
            _progress = 0.4;
          });
        } else {
          // Show dialog to ask user to grant permissions manually
          if (mounted) {
            // Check which permissions are not granted
            showPermissionInstructionsDialog();
          }
          return;
        }
      }
    }

    // Initialize Supabase
    if (!mounted) return;

    setState(() {
      _statusMessage = 'Initializing Supabase...';
      _progress = 0.5;
    });

    try {
      await SupabaseService.initialize();
      if (!mounted) return;
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _statusMessage = 'Failed to initialize Supabase: $e';
      });
      return;
    }

    // Create directory structure
    if (!mounted) return;

    setState(() {
      _statusMessage = 'Creating directory structure...';
      _progress = 0.7;
    });

    try {
      await StorageService.createDirectoryStructure();
      if (!mounted) return;
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _statusMessage = 'Failed to create directories: $e';
      });
      return;
    }

    // Initialization complete
    if (!mounted) return;

    setState(() {
      _statusMessage = 'Initialization complete';
      _progress = 1.0;
    });

    // Load settings to check registration status
    final settings = await Settings.load();

    // Wait a moment to show the completed progress bar
    await Future.delayed(const Duration(milliseconds: 500));

    if (!mounted) return;

    // Navigate to the appropriate screen
    if (settings == null || settings.code.isEmpty) {
      // If settings don't exist or code is empty, navigate to registration screen
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => const RegistrationScreen(),
        ),
      );
    } else {
      // Check if we need to fetch data first
      final signageDir = await StorageService.signageDirectory;
      final dataDir = Directory('$signageDir/data');

      // Ensure data directory exists
      if (!await dataDir.exists()) {
        await dataDir.create(recursive: true);
      }

      final mediaJsonFile = File('${dataDir.path}/media.json');
      final campaignsJsonFile = File('${dataDir.path}/campaigns.json');
      final scheduleJsonFile = File('${dataDir.path}/schedule.json');

      // If any of the data files don't exist, we need to fetch data
      final needsDataFetch = !(await mediaJsonFile.exists() &&
                              await campaignsJsonFile.exists() &&
                              await scheduleJsonFile.exists());

      if (!mounted) return;

      if (needsDataFetch) {
        // Navigate to data loading screen to fetch data
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const DataLoadingScreen(),
          ),
        );
      } else {
        // Navigate directly to player screen
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const PlayerScreen(),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: null, // Remove app bar
      extendBodyBehindAppBar: true, // Extend body behind app bar
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Logo
            Image.asset(
              'assets/images/logo.png',
              width: 200,
              height: 200,
            ),
            const SizedBox(height: 20),

            // Header text
            const Text(
              'Signage Player',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),

            // Version text
            Text(
              'Version $_appVersion',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),

            const SizedBox(height: 30),

            // Status message
            Text(
              _statusMessage,
              style: const TextStyle(fontSize: 14),
            ),

            const SizedBox(height: 10),

            // Loading bar
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 50),
              child: LinearProgressIndicator(
                value: _progress,
                backgroundColor: Colors.grey[200],
                valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
