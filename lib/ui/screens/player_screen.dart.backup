// This is a backup of the original player_screen.dart file
// Created during refactoring on $(date)

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:signage/core/models/schedule_item.dart';
import 'package:signage/core/models/settings.dart';
import 'package:signage/core/services/campaign_controller.dart';
import 'package:signage/ui/widgets/image_timer_widget.dart';
import 'package:signage/ui/widgets/slide_widget.dart';
import 'package:signage/ui/widgets/video_widget.dart';
import 'package:signage/utils/fullscreen_utils.dart';
// New widget imports for refactoring
import 'package:signage/ui/widgets/simple_media_widget.dart';
import 'package:signage/ui/widgets/slide_show_widget.dart';

class PlayerScreen extends StatefulWidget {
  const PlayerScreen({super.key});

  @override
  State<PlayerScreen> createState() => _PlayerScreenState();
}

class _PlayerScreenState extends State<PlayerScreen> {
  String _screenName = '';
  String _screenCode = '';
  bool _isLoading = true;
  String _errorMessage = '';

  // Media playback state
  Widget? _currentContentWidget;

  // For backward compatibility during refactoring
  Widget? _nextContentWidget;

  // Campaign controller
  late CampaignController _campaignController;

  // Schedule item tracking
  ScheduleItem? _currentScheduleItem;

  // For backward compatibility during refactoring
  ScheduleItem? _nextScheduleItem;

  // Cache for content widgets to improve transitions
  final Map<String, Widget> _contentWidgetCache = {};

  // For backward compatibility during refactoring
  bool _isPreloadingNextContent = false;
  bool _isTransitioning = false;
  final GlobalKey _nextContentKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    FullscreenUtils.ensureFullscreen();
    _campaignController = CampaignController();
    _initialize();
  }

  @override
  void dispose() {
    // Clear the content widget cache
    _contentWidgetCache.clear();

    // Reset content widget
    _currentContentWidget = null;

    // For backward compatibility during refactoring
    _nextContentWidget = null;

    // Reset schedule item
    _currentScheduleItem = null;
    _nextScheduleItem = null;

    // Reset transition state
    _isTransitioning = false;
    _isPreloadingNextContent = false;

    // Dispose the campaign controller
    _campaignController.dispose();

    super.dispose();
  }

  /// Initialize the player
  Future<void> _initialize() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });

      // Load settings
      await _loadSettings();

      // Initialize the campaign controller
      await _campaignController.initialize();

      // Start playback
      _startPlayback();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to initialize player: $e';
      });
    }
  }

  /// Load settings
  Future<void> _loadSettings() async {
    final settings = await Settings.load();
    if (settings != null && mounted) {
      setState(() {
        _screenName = settings.screenName;
        _screenCode = settings.code;
      });
    }
  }

  /// Start media playback
  void _startPlayback() {
    // Check if we have campaigns and schedule items
    if (_campaignController.campaigns.isEmpty) {
      setState(() {
        _errorMessage = 'No active campaigns found';
      });
      return;
    }

    if (_campaignController.scheduleItems.isEmpty) {
      setState(() {
        _errorMessage = 'No schedule items found';
      });
      return;
    }

    // Get the current schedule item
    final scheduleItem = _campaignController.currentScheduleItem;
    if (scheduleItem == null) {
      setState(() {
        _errorMessage = 'No current schedule item';
      });
      return;
    }

    // Play the schedule item
    _playScheduleItem(scheduleItem);
  }

  /// Create a content widget for a schedule item
  Future<Widget> _createContentWidget(ScheduleItem scheduleItem) async {
    // Check the campaign type
    if (scheduleItem.campaignType == 0) {
      // Simple media campaign
      debugPrint('====================================================');
      debugPrint('Simple media is created.....');
      debugPrint('====================================================');
      return await _createSimpleMediaWidget(scheduleItem);
    } else if (scheduleItem.campaignType == 1) {
      // Slide campaign
      return _createSlideWidget(scheduleItem);
    } else {
      // Unknown campaign type
      return Center(
        child: Text(
          'Unknown campaign type: ${scheduleItem.campaignType}',
          style: const TextStyle(color: Colors.white),
        ),
      );
    }
  }

  /// Create a simple media widget (image or video)
  Future<Widget> _createSimpleMediaWidget(ScheduleItem scheduleItem) async {
    // Create a cache key for this schedule item
    final cacheKey = 'simple-${scheduleItem.id}';

    // Check if we already have this widget in the cache
    if (_contentWidgetCache.containsKey(cacheKey)) {
      debugPrint('Using cached widget for ${scheduleItem.name}');
      return _contentWidgetCache[cacheKey]!;
    }

    debugPrint('Creating new widget for ${scheduleItem.name}');

    // Start getting the file path but don't await it yet
    final filePathFuture = _campaignController.getMediaFilePath(scheduleItem.name);

    // Get the screen size - this is safe even if not mounted because we'll check later
    final screenSize = MediaQuery.of(context).size;

    // Create a widget based on the file type
    Widget contentWidget;

    // Check if it's an image or video
    if (_campaignController.isImageFile(scheduleItem.name)) {
      // Create an image timer widget with FutureBuilder to handle the file path
      contentWidget = FutureBuilder<String?>(
        key: ValueKey('future-image-${scheduleItem.id}'),
        future: filePathFuture,
        builder: (context, snapshot) {
          if (snapshot.hasData && snapshot.data != null) {
            return ImageTimerWidget(
              key: ValueKey('image-${scheduleItem.id}'),
              filePath: snapshot.data!,
              width: screenSize.width,
              height: screenSize.height,
              durationInSeconds: 8, // Standard 8 seconds for images
              onComplete: _onContentComplete,
            );
          } else if (snapshot.hasError) {
            debugPrint('Error loading image file path: ${snapshot.error}');
            // Call onComplete to move to next item on error
            _onContentComplete();
            // Return a black container instead of an error message
            return Container(color: Colors.black);
          } else {
            // Return a black container while loading
            return Container(color: Colors.black);
          }
        },
      );
    } else if (_campaignController.isVideoFile(scheduleItem.name)) {
      // Create a video widget with FutureBuilder to handle the file path
      contentWidget = FutureBuilder<String?>(
        key: ValueKey('future-video-${scheduleItem.id}'),
        future: filePathFuture,
        builder: (context, snapshot) {
          if (snapshot.hasData && snapshot.data != null) {
            return VideoWidget(
              key: ValueKey('video-${scheduleItem.id}'),
              filePath: snapshot.data!,
              width: screenSize.width,
              height: screenSize.height,
              loop: false,
              onEnded: _onContentComplete,
              onError: (error) {
                debugPrint('Error playing video: $error');
                _onContentComplete();
              },
            );
          } else if (snapshot.hasError) {
            debugPrint('Error loading video file path: ${snapshot.error}');
            // Call onComplete to move to next item on error
            _onContentComplete();
            // Return a black container instead of an error message
            return Container(color: Colors.black);
          } else {
            // Return a black container while loading
            return Container(color: Colors.black);
          }
        },
      );
    } else {
      // Unknown file type - return a black container and move to next item
      contentWidget = Container(color: Colors.black);
      // Schedule moving to the next item
      Future.microtask(() {
        debugPrint('Unknown file type for ${scheduleItem.name}, moving to next item');
        _onContentComplete();
      });
    }

    // Store the widget in the cache
    _contentWidgetCache[cacheKey] = contentWidget;

    return contentWidget;
  }

  /// Create a slide widget
  Widget _createSlideWidget(ScheduleItem scheduleItem) {
    return SlideWidget(
      key: ValueKey('slide-${scheduleItem.id}'),
      scheduleItem: scheduleItem,
      campaignController: _campaignController,
      onComplete: _onContentComplete,
    );
  }

  /// Play a schedule item
  void _playScheduleItem(ScheduleItem scheduleItem) {
    debugPrint('Playing schedule item: ${scheduleItem.name} (type: ${scheduleItem.campaignType == 0 ? "SimpleMedia" : "Slide"})');

    // Store the current schedule item
    _currentScheduleItem = scheduleItem;

    // Create and display the content widget using the new simplified approach
    _createSimplifiedContentWidget(scheduleItem);

    // For backward compatibility during refactoring, still use the transition mechanism
    // if we already have content displayed
    if (_currentContentWidget != null && !_useSimplifiedApproach) {
      // Start transition process
      _startTransitionToContent(scheduleItem);
    } else {
      // First content or using simplified approach - load directly
      _loadContentDirectly(scheduleItem);
    }
  }

  // Flag to control whether to use the simplified approach
  // Set to true to enable the new content advancement system
  final bool _useSimplifiedApproach = true;

  /// Create a content widget using the simplified approach
  Future<void> _createSimplifiedContentWidget(ScheduleItem scheduleItem) async {
    // Get the screen size
    final screenSize = MediaQuery.of(context).size;

    // Create the appropriate widget based on the schedule item type
    Widget contentWidget;

    if (scheduleItem.campaignType == 0) {
      // Simple media (image or video)
      final filePath = await _campaignController.getMediaFilePath(scheduleItem.name);
      if (filePath == null) {
        debugPrint('Failed to get file path for ${scheduleItem.name}');
        _onContentComplete();
        return;
      }

      // Check if it's an image or video
      final isImage = _campaignController.isImageFile(scheduleItem.name);
      final isVideo = _campaignController.isVideoFile(scheduleItem.name);

      contentWidget = SimpleMediaWidget(
        id: scheduleItem.id,
        name: scheduleItem.name,
        filePath: filePath,
        width: screenSize.width,
        height: screenSize.height,
        isImage: isImage,
        isVideo: isVideo,
        onComplete: _onContentComplete,
      );
    } else if (scheduleItem.campaignType == 1) {
      // Slide show
      contentWidget = SlideShowWidget(
        scheduleItem: scheduleItem,
        campaignController: _campaignController,
        width: screenSize.width,
        height: screenSize.height,
        onComplete: _onContentComplete,
      );
    } else {
      // Unknown type, move to next item
      debugPrint('Unknown campaign type: ${scheduleItem.campaignType}');
      _onContentComplete();
      return;
    }

    // Update the UI with the new content widget
    if (mounted) {
      setState(() {
        _currentContentWidget = contentWidget;
      });
    }
  }

  /// Load content directly (for first content or after errors)
  void _loadContentDirectly(ScheduleItem scheduleItem) {
    // Store the current schedule item
    _currentScheduleItem = scheduleItem;

    // Create the content widget
    _createContentWidget(scheduleItem).then((widget) {
      if (mounted) {
        setState(() {
          // Set the new content as the current content
          _currentContentWidget = widget;
        });

        // Preload the next content to ensure smooth transitions
        debugPrint('****************************');
        debugPrint('PRELOADING NEXT CONTENT');
        debugPrint('****************************');
        _preloadNextContent();
      }
    }).catchError((error) {
      // Handle any errors during content creation
      debugPrint('Error creating content for ${scheduleItem.name}: $error');

      if (mounted) {
        // Move to the next item if there's an error
        _onContentComplete();
      }
    });
  }

  /// Start transition to new content while keeping current content visible
  void _startTransitionToContent(ScheduleItem scheduleItem) {
    // Don't start a new transition if one is already in progress
    if (_isTransitioning) {
      debugPrint('Transition already in progress, skipping transition to ${scheduleItem.name}');
      return;
    }

    debugPrint('Starting transition to ${scheduleItem.name}');

    // Mark that we're in a transition
    _isTransitioning = true;

    // Store the next schedule item
    _nextScheduleItem = scheduleItem;

    // For slide widgets, we need to ensure the content is preloaded
    if (scheduleItem.campaignType == 1) {
      _preloadSlideMediaFiles(scheduleItem);
    }

    // Check if we already have this widget in the cache
    final cacheKey = scheduleItem.campaignType == 0
        ? 'simple-${scheduleItem.id}'
        : 'slide-${scheduleItem.id}';

    if (_contentWidgetCache.containsKey(cacheKey)) {
      debugPrint('Using cached widget for transition to ${scheduleItem.name}');

      if (mounted) {
        setState(() {
          // Set the next content widget (initially invisible)
          _nextContentWidget = _contentWidgetCache[cacheKey];

          // For slide widgets, immediately check if it's ready
          if (scheduleItem.campaignType == 1) {
            // This will trigger the ready check immediately instead of waiting for the next frame
            _checkNextContentReady();
          }
        });
      }
      return;
    }

    // Create the next content widget
    _createContentWidget(scheduleItem).then((widget) {
      if (mounted && _isTransitioning) {
        setState(() {
          // Set the next content widget (initially invisible)
          _nextContentWidget = widget;

          // No need for a delay here - the _checkNextContentReady method will handle this
          // for both slide and simple media widgets
        });
      }
    }).catchError((error) {
      // Handle any errors during content creation
      debugPrint('Error creating next content for ${scheduleItem.name}: $error');

      if (mounted) {
        // Reset transition state
        _isTransitioning = false;
        _nextScheduleItem = null;

        // Move to the next item if there's an error
        _onContentComplete();
      }
    });
  }

  /// Preload the next content to ensure smooth transitions
  void _preloadNextContent() {
    // Don't preload if we're already preloading or in a transition
    if (_isPreloadingNextContent || _isTransitioning) {
      return;
    }

    _isPreloadingNextContent = true;

    try {
      // Get the index of the next schedule item
      final currentIndex = _campaignController.scheduleItems.indexOf(_currentScheduleItem!);
      if (currentIndex == -1) {
        _isPreloadingNextContent = false;
        return;
      }

      // Calculate the next index
      final nextIndex = (currentIndex + 1) % _campaignController.scheduleItems.length;

      // Get the next schedule item
      final nextItem = _campaignController.scheduleItems[nextIndex];

      // Don't preload if it's the same as the current item
      if (nextItem.id == _currentScheduleItem?.id) {
        _isPreloadingNextContent = false;
        return;
      }

      debugPrint('Preloading next content: ${nextItem.name}');

      // Create a cache key for this item
      final cacheKey = nextItem.campaignType == 0
          ? 'simple-${nextItem.id}'
          : 'slide-${nextItem.id}';

      // Check if we already have this widget in the cache
      if (_contentWidgetCache.containsKey(cacheKey)) {
        debugPrint('Next content already in cache: ${nextItem.name}');
        _isPreloadingNextContent = false;
        return;
      }

      // For SlideWidget, preload all media files in advance
      if (nextItem.campaignType == 1 && nextItem.content != null) {
        _preloadSlideMediaFiles(nextItem);
      }

      // Preload the next item by creating its widget
      // This will cache the widget but not display it yet
      _createContentWidget(nextItem).then((widget) {
        debugPrint('Preloaded next content: ${nextItem.name}');
        _isPreloadingNextContent = false;
      }).catchError((error) {
        debugPrint('Error preloading next content: $error');
        _isPreloadingNextContent = false;
      });
    } catch (e) {
      debugPrint('Error in preloadNextContent: $e');
      _isPreloadingNextContent = false;
    }
  }

  /// Preload all media files for a slide widget
  void _preloadSlideMediaFiles(ScheduleItem slideItem) {
    try {
      // Parse content if it's a string
      final dynamic contentData = slideItem.content is String
          ? jsonDecode(slideItem.content as String)
          : slideItem.content;

      if (contentData is List) {
        // Find all playlist items
        for (final item in contentData) {
          if (item['type'] == 'playlist' &&
              item['content'] != null &&
              item['content']['playlist'] != null) {

            final playlist = item['content']['playlist'] as List;

            // Preload all media files in the playlist
            for (final playlistItem in playlist) {
              final String name = playlistItem['name'] ?? '';
              if (name.isNotEmpty) {
                // Preload the file path
                _campaignController.getMediaFilePath(name).then((path) {
                  if (path != null) {
                    debugPrint('Preloaded media file path: $path');
                  }
                });
              }
            }
          }
        }
      }
    } catch (e) {
      debugPrint('Error preloading slide media files: $e');
    }
  }

  /// Handle content completion
  void _onContentComplete() {
    // Prevent multiple calls or calls during transition
    if (_currentScheduleItem == null || (_isTransitioning && !_useSimplifiedApproach)) {
      return;
    }

    debugPrint('Content completed, moving to next item');
    debugPrint('Current schedule item: ${_currentScheduleItem?.name}');

    // Store the current item for cache cleanup
    final completedItem = _currentScheduleItem;

    // Move to the next schedule item
    _campaignController.nextScheduleItem();

    // Get the new schedule item
    final nextScheduleItem = _campaignController.currentScheduleItem;
    if (nextScheduleItem != null) {
      debugPrint('====================================================');
      debugPrint('Playing next schedule item: ${nextScheduleItem.name}');
      debugPrint('====================================================');

      if (_useSimplifiedApproach) {
        // Simplified approach: directly create and display the next content
        _createSimplifiedContentWidget(nextScheduleItem);
      } else {
        // Legacy approach: use the transition mechanism
        _playScheduleItem(nextScheduleItem);
      }

      // Clean up the cache for the completed item after a delay
      // This ensures the old content is fully replaced before disposal
      Future.delayed(const Duration(seconds: 5), () {
        if (completedItem != null && !_isTransitioning) {
          // Only clean up if we're not in the middle of a transition
          // Create the appropriate cache key based on content type
          final cacheKey = completedItem.campaignType == 0
              ? 'simple-${completedItem.id}'
              : 'slide-${completedItem.id}';

          // Double-check that this item is not the current or next item
          final isCurrentItem = _currentScheduleItem?.id == completedItem.id;
          final isNextItem = _nextScheduleItem?.id == completedItem.id;

          if (!isCurrentItem && !isNextItem && _contentWidgetCache.containsKey(cacheKey)) {
            debugPrint('====================================================');
            debugPrint('Removing cached widget for ${completedItem.name}');
            debugPrint('====================================================');
            _contentWidgetCache.remove(cacheKey);
          }
        }

        // Limit cache size to prevent memory issues
        if (_contentWidgetCache.length > 5) {
          debugPrint('Cache size exceeded limit, clearing older entries');
          // Keep only the most recent entries
          final keysToRemove = _contentWidgetCache.keys.take(_contentWidgetCache.length - 5).toList();
          for (final key in keysToRemove) {
            _contentWidgetCache.remove(key);
          }
        }
      });
    } else {
      debugPrint('No more schedule items, moving to next campaign');
      // No more schedule items, move to the next campaign
      _campaignController.nextCampaign();

      // Clear the cache when moving to a new campaign
      _contentWidgetCache.clear();

      // Reset transition state
      _isTransitioning = false;
      _nextContentWidget = null;
      _nextScheduleItem = null;

      // Start playback again
      _startPlayback();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: null, // Remove app bar
      extendBodyBehindAppBar: true, // Extend body behind app bar
      body: _buildBody(),
    );
  }

  /// Build the body of the screen
  Widget _buildBody() {
    if (_isLoading) {
      // Show a loading indicator
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_errorMessage.isNotEmpty) {
      // Show an error message
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              'Error',
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _initialize,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    // Handle content display
    if (_currentContentWidget != null) {
      if (_useSimplifiedApproach) {
        // Simplified approach: just show the current content directly
        return _currentContentWidget!;
      } else if (_nextContentWidget != null && _isTransitioning) {
        // Legacy approach with transitions: use a Stack with both widgets
        return Stack(
          children: [
            // Current content is always visible at the bottom layer
            // This ensures there's never a black screen
            _currentContentWidget!,

            // Next content is initially loaded but not visible
            // Using Offstage to ensure it's properly initialized but not visible
            Offstage(
              offstage: true, // Completely hidden but still initialized
              child: KeyedSubtree(
                key: _nextContentKey,
                child: _nextContentWidget!,
              ),
            ),

            // Invisible listener widget that will detect when the next content is ready
            _buildReadyDetector(),
          ],
        );
      } else {
        // Legacy approach without transitions: just show the current content
        return KeyedSubtree(
          key: ValueKey('current-content-${_currentScheduleItem?.id}'),
          child: _currentContentWidget!,
        );
      }
    }

    // Show a placeholder
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Text(
            'Player Screen',
            style: TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 20),

          // Screen info
          Text(
            'Screen: $_screenName',
            style: const TextStyle(
              fontSize: 18,
              color: Colors.white,
            ),
          ),
          Text(
            'Code: $_screenCode',
            style: const TextStyle(
              fontSize: 18,
              color: Colors.white,
            ),
          ),

          const SizedBox(height: 40),

          // Status message
          const Text(
            'No content to play',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
          ),
        ],
      ),
    );
  }

  /// Build a widget that detects when the next content is ready
  Widget _buildReadyDetector() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Schedule a check for when the next content is ready
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_isTransitioning && _nextContentWidget != null) {
            _checkNextContentReady();
          }
        });

        // Return an invisible widget that doesn't affect layout
        return const SizedBox.shrink();
      },
    );
  }

  /// Check if the next content is ready and complete the transition if it is
  void _checkNextContentReady() {
    if (!_isTransitioning || _nextContentWidget == null) return;

    // For SlideWidget, check if it's fully initialized
    if (_nextScheduleItem?.campaignType == 1) {
      // Get the SlideWidget instance
      final slideWidget = _nextContentWidget as SlideWidget;

      // Check if the SlideWidget is fully initialized
      if (slideWidget.isFullyInitialized()) {
        debugPrint('SlideWidget is fully initialized, completing transition');
        _completeTransition();
      } else {
        // Check again after a short delay
        Future.delayed(const Duration(milliseconds: 50), () {
          if (mounted && _isTransitioning) {
            _checkNextContentReady();
          }
        });
      }
    } else {
      // For simple media, complete the transition immediately
      debugPrint('Simple media is ready, completing transition');
      _completeTransition();
    }
  }

  /// Complete the transition by making the next content visible
  void _completeTransition() {
    if (!mounted || !_isTransitioning) return;

    debugPrint('Completing transition to ${_nextScheduleItem?.name}');

    // Store references to avoid null issues
    final nextWidget = _nextContentWidget;
    final nextItem = _nextScheduleItem;

    if (nextWidget == null || nextItem == null) {
      debugPrint('Warning: Next widget or item is null during transition completion');
      _isTransitioning = false;
      return;
    }

    setState(() {
      // Make the next content the current content
      _currentContentWidget = nextWidget;
      _currentScheduleItem = nextItem;

      // Reset transition state
      _nextContentWidget = null;
      _nextScheduleItem = null;
      _isTransitioning = false;
    });

    // Preload the next content after completing this transition
    // This ensures we always have the next content ready
    Future.microtask(() {
      debugPrint('Preloading next content after transition completion');
      _preloadNextContent();
    });
  }
}
