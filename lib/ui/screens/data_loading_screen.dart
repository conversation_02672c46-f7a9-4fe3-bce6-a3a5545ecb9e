import 'package:flutter/material.dart';
import 'package:signage/core/services/data_fetch_service.dart';
import 'package:signage/core/services/logging_service.dart';
import 'package:signage/ui/screens/player_screen.dart';
import 'package:signage/utils/fullscreen_utils.dart';

class DataLoadingScreen extends StatefulWidget {
  const DataLoadingScreen({super.key});

  @override
  State<DataLoadingScreen> createState() => _DataLoadingScreenState();
}

class _DataLoadingScreenState extends State<DataLoadingScreen> {
  double _progress = 0.0;
  String _statusMessage = 'Preparing to fetch data...';
  String? _errorMessage;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    FullscreenUtils.ensureFullscreen();
    _fetchData();
  }

  /// Fetch data from Supabase
  Future<void> _fetchData() async {
    // Start the logging service to ensure data and media file downloads are logged
    final loggingService = LoggingService();
    await loggingService.start();

    final dataFetchService = DataFetchService(
      onProgress: (progress, message) {
        if (mounted) {
          setState(() {
            _progress = progress;
            _statusMessage = message;
          });
        }
      },
      onComplete: () {
        if (mounted) {
          // Navigate to player screen
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => const PlayerScreen(),
            ),
          );
        }
      },
      onError: (error) {
        if (mounted) {
          setState(() {
            _isLoading = false;
            _errorMessage = error;
          });
        }
      },
    );

    await dataFetchService.fetchAllData();
  }

  /// Retry fetching data
  void _retry() {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _progress = 0.0;
      _statusMessage = 'Preparing to fetch data...';
    });

    _fetchData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: null, // Remove app bar
      extendBodyBehindAppBar: true, // Extend body behind app bar
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Logo
              Image.asset(
                'assets/images/logo.png',
                width: 150,
                height: 150,
              ),
              const SizedBox(height: 20),

              // Header text
              const Text(
                'Signage Player',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 40),

              // Status message
              Text(
                _statusMessage,
                style: const TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),

              // Progress bar
              if (_isLoading)
                LinearProgressIndicator(
                  value: _progress,
                  backgroundColor: Colors.grey[300],
                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
                  minHeight: 10,
                ),

              // Error message
              if (_errorMessage != null) ...[
                const SizedBox(height: 20),
                Text(
                  'Error: $_errorMessage',
                  style: const TextStyle(
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: _retry,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Retry'),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
