import 'dart:convert';

/// Model class representing a campaign schedule from the Supabase 'campaign_schedules' table
class Schedule {
  final String id; // Changed from int to String for UUID
  final String campaignId; // Changed from int to String for UUID
  final String mediaId; // Changed from int to String for UUID
  final int displayOrder;
  final int? displayDuration; // in seconds, for images
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic>? metadata;

  Schedule({
    required this.id,
    required this.campaignId,
    required this.mediaId,
    required this.displayOrder,
    this.displayDuration,
    this.createdAt,
    this.updatedAt,
    this.metadata,
  });

  /// Create a Schedule from a JSON map
  factory Schedule.fromJson(Map<String, dynamic> json) {
    return Schedule(
      id: json['id']?.toString() ?? '', // Ensure id is a string
      campaignId: json['campaign_id']?.toString() ?? '', // Ensure campaignId is a string
      mediaId: json['media_id']?.toString() ?? '', // Ensure mediaId is a string
      displayOrder: json['display_order'] != null
          ? int.tryParse(json['display_order'].toString()) ?? 0
          : 0,
      displayDuration: json['display_duration'] != null
          ? int.tryParse(json['display_duration'].toString())
          : null,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
      metadata: json['metadata'] != null
          ? (json['metadata'] is String
              ? jsonDecode(json['metadata'])
              : json['metadata'])
          : null,
    );
  }

  /// Convert Schedule to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'campaign_id': campaignId,
      'media_id': mediaId,
      'display_order': displayOrder,
      'display_duration': displayDuration,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'metadata': metadata != null
          ? (metadata is String ? metadata : jsonEncode(metadata))
          : null,
    };
  }

  @override
  String toString() {
    return 'Schedule(id: $id, campaignId: $campaignId, mediaId: $mediaId, displayOrder: $displayOrder)';
  }
}
