import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:signage/utils/platform_utils.dart';

class StorageService {
  static Future<String> get _baseDirectory async {
    if (PlatformUtils.isAndroid) {
      // Use external storage on Android
      final directory = await getExternalStorageDirectory();
      return directory?.path ?? (await getApplicationDocumentsDirectory()).path;
    } else if (PlatformUtils.isWindows) {
      // Use %APPDATA%/Local on Windows
      final directory = await getApplicationSupportDirectory();
      return directory.path;
    } else {
      // Use ~/Documents on Linux
      final directory = await getApplicationDocumentsDirectory();
      return directory.path;
    }
  }

  static Future<String> get signageDirectory async {
    final baseDir = await _baseDirectory;
    final signageDir = Directory('$baseDir/signage');
    
    if (!await signageDir.exists()) {
      await signageDir.create(recursive: true);
    }
    
    return signageDir.path;
  }

  static Future<void> createDirectoryStructure() async {
    final signageDir = await signageDirectory;
    
    // Create required subdirectories
    final contentDir = Directory('$signageDir/content');
    final dataDir = Directory('$signageDir/data');
    final tempDir = Directory('$signageDir/temp');
    
    if (!await contentDir.exists()) {
      await contentDir.create();
    }
    
    if (!await dataDir.exists()) {
      await dataDir.create();
    }
    
    if (!await tempDir.exists()) {
      await tempDir.create();
    }
    
    // Log the created directory structure
    print('Created signage directory structure at: $signageDir');
    print('Content directory: ${contentDir.path}');
    print('Data directory: ${dataDir.path}');
    print('Temp directory: ${tempDir.path}');
  }
}
