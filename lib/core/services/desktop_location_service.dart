import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:geolocator/geolocator.dart';

class DesktopLocationService {
  /// Get device location on desktop platforms (Windows, macOS) using IP-based geolocation
  static Future<Position> getDesktopLocation() async {
    try {
      // For Windows, try Windows Location API first (if available)
      if (Platform.isWindows) {
        final windowsLocation = await _tryWindowsLocationAPI();
        if (windowsLocation != null) {
          debugPrint('Location obtained from Windows Location API: ${windowsLocation.latitude}, ${windowsLocation.longitude}');
          return windowsLocation;
        }
      }
      
      // For macOS, try Core Location (if available)
      if (Platform.isMacOS) {
        final macOSLocation = await _tryMacOSCoreLocation();
        if (macOSLocation != null) {
          debugPrint('Location obtained from macOS Core Location: ${macOSLocation.latitude}, ${macOSLocation.longitude}');
          return macOSLocation;
        }
      }
      
      // If platform-specific location services fail, fall back to IP-based geolocation
      final ipBasedLocation = await _getLocationFromIP();
      debugPrint('Location obtained from IP: ${ipBasedLocation.latitude}, ${ipBasedLocation.longitude}');
      return ipBasedLocation;
    } catch (e) {
      debugPrint('Error getting desktop location: $e');
      // Fallback to New York coordinates
      return Position(
        longitude: -74.0060,
        latitude: 40.7128,
        timestamp: DateTime.now(),
        accuracy: 0.0,
        altitude: 0.0,
        altitudeAccuracy: 0.0,
        heading: 0.0,
        headingAccuracy: 0.0,
        speed: 0.0,
        speedAccuracy: 0.0,
      );
    }
  }

  /// Try to get location using Windows Location API
  static Future<Position?> _tryWindowsLocationAPI() async {
    try {
      // Use PowerShell to access Windows Location API
      final result = await Process.run('powershell', [
        '-Command',
        '''
        Add-Type -AssemblyName System.Device
        \$watcher = New-Object System.Device.Location.GeoCoordinateWatcher
        \$watcher.Start()
        Start-Sleep -Seconds 3
        if (\$watcher.Position.Location.IsUnknown -eq \$false) {
          Write-Output "\$(\$watcher.Position.Location.Latitude),\$(\$watcher.Position.Location.Longitude)"
        }
        \$watcher.Stop()
        '''
      ]);
      
      if (result.exitCode == 0 && result.stdout.toString().trim().isNotEmpty) {
        final locationStr = result.stdout.toString().trim();
        final parts = locationStr.split(',');
        
        if (parts.length == 2) {
          final latitude = double.tryParse(parts[0]);
          final longitude = double.tryParse(parts[1]);
          
          if (latitude != null && longitude != null && latitude != 0.0 && longitude != 0.0) {
            return Position(
              longitude: longitude,
              latitude: latitude,
              timestamp: DateTime.now(),
              accuracy: 100.0, // Windows Location API accuracy
              altitude: 0.0,
              altitudeAccuracy: 0.0,
              heading: 0.0,
              headingAccuracy: 0.0,
              speed: 0.0,
              speedAccuracy: 0.0,
            );
          }
        }
      }
      
      return null; // Windows Location API not available or failed
    } catch (e) {
      debugPrint('Error accessing Windows Location API: $e');
      return null;
    }
  }

  /// Try to get location using macOS Core Location
  static Future<Position?> _tryMacOSCoreLocation() async {
    try {
      // Use osascript to access Core Location on macOS
      final result = await Process.run('osascript', [
        '-e',
        '''
        tell application "System Events"
          try
            set locationData to do shell script "curl -s 'https://ipinfo.io/json'"
            return locationData
          end try
        end tell
        '''
      ]);
      
      if (result.exitCode == 0 && result.stdout.toString().trim().isNotEmpty) {
        try {
          final data = jsonDecode(result.stdout.toString());
          final locationStr = data['loc'] as String?;
          
          if (locationStr != null) {
            final parts = locationStr.split(',');
            if (parts.length == 2) {
              final latitude = double.tryParse(parts[0]);
              final longitude = double.tryParse(parts[1]);
              
              if (latitude != null && longitude != null) {
                return Position(
                  longitude: longitude,
                  latitude: latitude,
                  timestamp: DateTime.now(),
                  accuracy: 1000.0, // IP-based accuracy
                  altitude: 0.0,
                  altitudeAccuracy: 0.0,
                  heading: 0.0,
                  headingAccuracy: 0.0,
                  speed: 0.0,
                  speedAccuracy: 0.0,
                );
              }
            }
          }
        } catch (e) {
          debugPrint('Error parsing macOS location data: $e');
        }
      }
      
      return null; // macOS Core Location not available or failed
    } catch (e) {
      debugPrint('Error accessing macOS Core Location: $e');
      return null;
    }
  }

  /// Get location based on IP address using a free geolocation API
  static Future<Position> _getLocationFromIP() async {
    try {
      // Try multiple IP geolocation services for better reliability
      final services = [
        'https://ipinfo.io/json',
        'https://ipapi.co/json/',
        'https://freegeoip.app/json/',
      ];
      
      for (final serviceUrl in services) {
        try {
          final response = await http.get(
            Uri.parse(serviceUrl),
            headers: {'User-Agent': 'Digital Signage Player'},
          ).timeout(const Duration(seconds: 5));
          
          if (response.statusCode == 200) {
            final data = jsonDecode(response.body);
            
            // Handle different API response formats
            String? locationStr;
            double? latitude, longitude;
            
            if (serviceUrl.contains('ipinfo.io')) {
              locationStr = data['loc'] as String?;
              if (locationStr != null) {
                final parts = locationStr.split(',');
                if (parts.length == 2) {
                  latitude = double.tryParse(parts[0]);
                  longitude = double.tryParse(parts[1]);
                }
              }
            } else {
              // For ipapi.co and freegeoip.app
              latitude = (data['latitude'] ?? data['lat'])?.toDouble();
              longitude = (data['longitude'] ?? data['lon'])?.toDouble();
            }
            
            if (latitude != null && longitude != null && latitude != 0.0 && longitude != 0.0) {
              debugPrint('Successfully obtained location from $serviceUrl');
              return Position(
                longitude: longitude,
                latitude: latitude,
                timestamp: DateTime.now(),
                accuracy: 1000.0, // IP geolocation is not very accurate
                altitude: 0.0,
                altitudeAccuracy: 0.0,
                heading: 0.0,
                headingAccuracy: 0.0,
                speed: 0.0,
                speedAccuracy: 0.0,
              );
            }
          }
        } catch (e) {
          debugPrint('Failed to get location from $serviceUrl: $e');
          continue; // Try next service
        }
      }
    } catch (e) {
      debugPrint('Error in IP-based geolocation: $e');
    }
    
    // Fallback to New York coordinates
    debugPrint('All location services failed, using fallback coordinates (New York)');
    return Position(
      longitude: -74.0060,
      latitude: 40.7128,
      timestamp: DateTime.now(),
      accuracy: 0.0,
      altitude: 0.0,
      altitudeAccuracy: 0.0,
      heading: 0.0,
      headingAccuracy: 0.0,
      speed: 0.0,
      speedAccuracy: 0.0,
    );
  }
}
