import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:signage/core/models/screen.dart';
import 'package:signage/core/models/campaign.dart';
import 'package:signage/core/models/media.dart';
import 'package:signage/core/models/schedule_item.dart';
import 'package:signage/core/models/screen_activity.dart';
import 'package:signage/core/models/proof_of_play.dart';
import 'package:signage/core/storage/storage_service.dart';

class SupabaseService {
  static const String supabaseUrl = 'https://oatyudgnhndxrwwlbxsj.supabase.co';
  static const String supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9hdHl1ZGduaG5keHJ3d2xieHNqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ4MDUwMDAsImV4cCI6MjA2MDM4MTAwMH0.FLeeCaNs_BAfMiPdi56SRYIS9XKk2nYIUS3ZmiyDGbE';

  static Future<void> initialize() async {
    await Supa<PERSON>.initialize(
      url: supabaseUrl,
      anonKey: supabase<PERSON><PERSON>Key,
      realtimeClientOptions: const RealtimeClientOptions(
        eventsPerSecond: 10,
      ),
    );

    // Log that Supabase has been initialized with realtime enabled
    debugLog('Supabase initialized with realtime enabled');
  }

  static SupabaseClient get client => Supabase.instance.client;

  /// Fetch screen info by registration code
  /// Returns null if no screen is found or if the screen is already registered
  static Future<Screen?> fetchScreenByCode(String code) async {
    try {
      // Ensure code is treated as a string
      final String codeString = code.toString();

      final response = await client
          .from('screens')
          .select()
          .eq('code', codeString)
          .eq('is_registered', false)
          .eq('is_deleted', false)
          .single();

      // Log response for debugging
      debugLog('Screen fetch response: $response');

      return Screen.fromJson(response);
    } catch (e) {
      debugLog('Error fetching screen by code: $e');
      return null;
    }
  }

  /// Update screen registration status
  static Future<bool> updateScreenRegistration(String screenId) async {
    try {
      await client
          .from('screens')
          .update(
            {
              'is_registered': true,
              'last_ping_at': DateTime.now().toUtc().toIso8601String()
            }
          )
          .eq('id', screenId);

      return true;
    } catch (e) {
      debugLog('Error updating screen registration: $e');
      return false;
    }
  }

  /// Register the player with screen_registrations table
  static Future<bool> registerPlayer(String screenId) async {
    try {
      final now = DateTime.now().toUtc();

      await client
          .from('screen_registrations')
          .insert({
            'screen_id': screenId,
            'registered_at': now.toIso8601String(),
          });

      return true;
    } catch (e) {
      debugLog('Error registering player: $e');
      return false;
    }
  }

  /// Fetch media files using the fetch_media_files RPC
  /// Requires screen ID and current date in UTC
  static Future<List<Media>> fetchMediaFiles(String screenId) async {
    try {
      // Get current date in UTC
      final now = DateTime.now().toUtc();
      final currentDate = now.toIso8601String().split('T')[0]; // Format: YYYY-MM-DD

      debugLog('Fetching media files with screenId: $screenId, date: $currentDate');

      final response = await client
          .rpc('fetch_media_files', params: {
            'input_screen_id': screenId,
            'input_date': currentDate,
          })
          .select();

      debugLog('Media files fetch response: $response');

      return (response as List)
          .map((item) => Media.fromJson(item))
          .toList();
    } catch (e) {
      debugLog('Error fetching media files: $e');
      return [];
    }
  }

  /// Fetch active campaigns for a screen
  static Future<List<Campaign>> fetchCampaigns(String screenId) async {
    try {
      // Get current date and time in UTC
      final now = DateTime.now().toUtc();
      final currentDateTime = now.toIso8601String();

      debugLog('Fetching campaigns with screenId: $screenId, date: ${currentDateTime.split('T')[0]}');

      // Direct query to get active campaigns for the screen
      final response = await client
          .from('campaign_screens')
          .select('campaign:campaigns(id, name, start_date, end_date, status, trigger_type, latitude, longitude, radius, min_temp, max_temp, units, distance)')
          .eq('screen_id', screenId)
          .eq('campaigns.status', 'active')
          .eq('campaigns.is_deleted', false)
          .lte('campaigns.start_date', currentDateTime)
          .gte('campaigns.end_date', currentDateTime);

      debugLog('Campaigns fetch response: $response');

      return (response as List)
          .map((item) => Campaign.fromJson(item['campaign']))
          .toList();
    } catch (e) {
      debugLog('Error fetching campaigns: $e');
      return [];
    }
  }

  /// Fetch campaign schedules using the fetch_screen_campaigns_schedule RPC
  static Future<List<ScheduleItem>> fetchSchedules(String screenId) async {
    try {
      // Get current date in UTC
      final now = DateTime.now().toUtc();
      final currentDate = now.toIso8601String().split('T')[0]; // Format: YYYY-MM-DD

      debugLog('Fetching schedules with screenId: $screenId, date: $currentDate');

      final response = await client
          .rpc('fetch_screen_campaigns_schedule', params: {
            'input_screen_id': screenId,
            'input_date': currentDate, // Add current date parameter if needed by the RPC
          })
          .select();

      debugLog('Schedules fetch response: $response');

      return (response as List)
          .map((item) => ScheduleItem.fromJson(item))
          .toList();
    } catch (e) {
      debugLog('Error fetching schedules: $e');
      return [];
    }
  }

  /// Download a file from Supabase storage
  static Future<Uint8List?> downloadFile(String filePath) async {
    try {
      final file = await client.storage.from('medialibrary').download(filePath);
      return file;
    } catch (e) {
      debugLog('Error downloading file $filePath: $e');
      return null;
    }
  }

  /// Save fetched data to JSON files in the temp directory
  static Future<void> saveDataToTempFiles({
    required List<Media> mediaList,
    required List<Campaign> campaignList,
    required List<ScheduleItem> scheduleList,
  }) async {
    final tempDir = Directory('${await StorageService.signageDirectory}/temp');

    // Ensure temp directory exists
    if (!await tempDir.exists()) {
      await tempDir.create(recursive: true);
    }

    // Save media.json
    final mediaJson = jsonEncode(mediaList.map((m) => m.toJson()).toList());
    final mediaFile = File('${tempDir.path}/media.json');
    await mediaFile.writeAsString(mediaJson);

    // Save campaigns.json
    final campaignsJson = jsonEncode(campaignList.map((c) => c.toJson()).toList());
    final campaignsFile = File('${tempDir.path}/campaigns.json');
    await campaignsFile.writeAsString(campaignsJson);

    // Save schedule.json
    final scheduleJson = jsonEncode(scheduleList.map((s) => s.toJson()).toList());
    final scheduleFile = File('${tempDir.path}/schedule.json');
    await scheduleFile.writeAsString(scheduleJson);

    debugLog('Data saved to temp files');
  }

  /// Move JSON files from temp to data directory
  static Future<void> moveFilesFromTempToData() async {
    final signageDir = await StorageService.signageDirectory;
    final tempDir = Directory('$signageDir/temp');
    final dataDir = Directory('$signageDir/data');

    // List of files to move
    final fileNames = ['media.json', 'campaigns.json', 'schedule.json'];

    for (final fileName in fileNames) {
      final sourceFile = File('${tempDir.path}/$fileName');
      final destFile = File('${dataDir.path}/$fileName');

      if (await sourceFile.exists()) {
        // Copy file content
        await destFile.writeAsString(await sourceFile.readAsString());
        debugLog('Moved $fileName from temp to data directory');
      }
    }
  }

  /// Update the screen details including last_ping_at and health information
  static Future<bool> updateScreenDetails(String screenId, Map<String, dynamic> healthInfo) async {
    try {
      final now = DateTime.now().toUtc();
      debugLog('Updating screen details for screen $screenId to ${now.toIso8601String()}');
      debugLog('Health info: $healthInfo');

      await client
          .from('screens')
          .update({
            'last_ping_at': now.toIso8601String(),
            'health': healthInfo
          })
          .eq('id', screenId);

      return true;
    } catch (e) {
      debugLog('Error updating screen details: $e');
      return false;
    }
  }

  /// Update the last_ping_at field for a screen (deprecated, use updateScreenDetails instead)
  @Deprecated('Use updateScreenDetails instead')
  static Future<bool> updateLastPing(String screenId) async {
    try {
      final now = DateTime.now().toUtc();
      debugLog('Updating last_ping_at for screen $screenId to ${now.toIso8601String()}');

      await client
          .from('screens')
          .update({'last_ping_at': now.toIso8601String()})
          .eq('id', screenId);

      return true;
    } catch (e) {
      debugLog('Error updating last_ping_at: $e');
      return false;
    }
  }

  /// Helper method for logging
  static void debugLog(String message) {
    // In a production app, you would use a proper logging framework
    // For now, we'll use print in debug mode only
    assert(() {
      print('[SupabaseService] $message');
      return true;
    }());
  }

  /// Log screen activity to the screen_activities table
  static Future<bool> logScreenActivity(ScreenActivity activity) async {
    try {
      debugLog('Logging screen activity: ${activity.logDetails}');
      debugLog('Activity data: ${activity.toJson()}');

      final response = await client
          .from('screen_activities')
          .insert(activity.toJson())
          .select();

      debugLog('Screen activity log response: $response');
      return true;
    } catch (e) {
      debugLog('Error logging screen activity: $e');
      // Print stack trace for better debugging
      debugLog('Stack trace: ${StackTrace.current}');
      return false;
    }
  }

  /// Log proof of play to the proof_of_play table
  static Future<bool> logProofOfPlay(ProofOfPlay proofOfPlay) async {
    try {
      debugLog('Logging proof of play: ${proofOfPlay.toString()}');

      // Create a modified JSON object based on whether this is a simple media or slide
      Map<String, dynamic> jsonData = proofOfPlay.toJson();

      // For simple media (empty slideId), explicitly set slide_id to null in the JSON
      if (proofOfPlay.slideId == "") {
        jsonData['slide_id'] = null;
        debugLog('Setting slide_id to null for simple media');
      }

      // Also handle empty mediaId values by setting them to null
      if (proofOfPlay.mediaId == null || proofOfPlay.mediaId == "") {
        jsonData['media_id'] = null;
        debugLog('Setting media_id to null for empty value');
      }

      debugLog('Proof of play data: $jsonData');

      final response = await client
          .from('proof_of_play')
          .insert(jsonData)
          .select();

      debugLog('Proof of play log response: $response');
      return true;
    } catch (e) {
      debugLog('Error logging proof of play: $e');
      // Print stack trace for better debugging
      debugLog('Stack trace: ${StackTrace.current}');
      return false;
    }
  }

  /// Delete existing file download records for a screen
  static Future<bool> deleteFileDownloadRecords(String screenId) async {
    try {
      debugLog('Deleting existing file download records for screen: $screenId');

      // First, query to find records that will be deleted (for logging purposes)
      final recordsToDelete = await client
          .from('screen_activities')
          .select('id, log_details')
          .eq('screen_id', screenId)
          .gt('file_download_count', 0);

      debugLog('Found ${recordsToDelete.length} file download records to delete: $recordsToDelete');

      // Then perform the delete operation and wait for it to complete
      await client
          .from('screen_activities')
          .delete()
          .eq('screen_id', screenId)
          .gt('file_download_count', 0);

      // Verify deletion by checking if records still exist
      final remainingRecords = await client
          .from('screen_activities')
          .select('count')
          .eq('screen_id', screenId)
          .gt('file_download_count', 0)
          .single();

      final count = remainingRecords['count'] as int;
      debugLog('Remaining file download records after deletion: $count');

      if (count > 0) {
        debugLog('WARNING: Not all file download records were deleted!');
      } else {
        debugLog('Successfully deleted all file download records');
      }

      return true;
    } catch (e) {
      debugLog('Error deleting file download records: $e');
      // Print stack trace for better debugging
      debugLog('Stack trace: ${StackTrace.current}');
      return false;
    }
  }

  /// Fetch trial and subscription data from screen_registrations table
  static Future<Map<String, dynamic>?> fetchTrialSubscriptionData(String screenId) async {
    try {
      debugLog('Fetching trial and subscription data for screen: $screenId');

      final response = await client
          .from('screen_registrations')
          .select('trial_ends_at, subscription_status')
          .eq('screen_id', screenId)
          .single();

      debugLog('Trial/subscription data fetch response: $response');

      return {
        'trial_ends_at': response['trial_ends_at'],
        'subscription_status': response['subscription_status'],
      };
    } catch (e) {
      debugLog('Error fetching trial/subscription data: $e');
      return null;
    }
  }
}
