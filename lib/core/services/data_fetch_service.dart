
import 'package:flutter/foundation.dart';
import 'package:signage/core/models/settings.dart';
import 'package:signage/core/services/file_downloader.dart';
import 'package:signage/core/services/logging_service.dart';
import 'package:signage/core/services/supabase_service.dart';

/// Service for fetching data from Supabase
class DataFetchService {
  /// Progress callback
  final void Function(double progress, String message)? onProgress;

  /// Complete callback
  final void Function()? onComplete;

  /// Error callback
  final void Function(String error)? onError;

  /// Whether to log data update completion
  final bool logDataUpdate;

  /// Constructor
  DataFetchService({
    this.onProgress,
    this.onComplete,
    this.onError,
    this.logDataUpdate = true, // Default to true for backward compatibility
  });

  /// Fetch all data and download media files
  Future<bool> fetchAllData() async {
    try {
      // Load settings
      final settings = await Settings.load();
      if (settings == null) {
        if (onError != null) {
          onError!('Settings not found. Please register the device first.');
        }
        return false;
      }

      // Update progress
      if (onProgress != null) {
        onProgress!(0.1, 'Fetching media files...');
      }

      // Fetch media files
      final mediaList = await SupabaseService.fetchMediaFiles(settings.screenId);
      if (mediaList.isEmpty) {
        if (onError != null) {
          onError!('No media files found for this screen.');
        }
        return false;
      }

      // Update progress
      if (onProgress != null) {
        onProgress!(0.2, 'Fetching campaigns...');
      }

      // Fetch campaigns
      final campaignList = await SupabaseService.fetchCampaigns(settings.screenId);
      if (campaignList.isEmpty) {
        if (onError != null) {
          onError!('No active campaigns found for this screen.');
        }
        return false;
      }

      // Update progress
      if (onProgress != null) {
        onProgress!(0.3, 'Fetching schedules...');
      }

      // Fetch schedules
      final scheduleList = await SupabaseService.fetchSchedules(settings.screenId);
      if (scheduleList.isEmpty) {
        if (onError != null) {
          onError!('No schedules found for this screen.');
        }
        return false;
      }

      // Update progress
      if (onProgress != null) {
        onProgress!(0.4, 'Saving data to temp files...');
      }

      // Save data to temp files
      await SupabaseService.saveDataToTempFiles(
        mediaList: mediaList,
        campaignList: campaignList,
        scheduleList: scheduleList,
      );

      // Update progress
      if (onProgress != null) {
        onProgress!(0.5, 'Downloading media files...');
      }

      // Download media files
      final downloader = FileDownloader(
        onProgress: (progress) {
          if (onProgress != null) {
            // Scale progress from 0.5 to 0.9
            onProgress!(0.5 + (progress * 0.4), 'Downloading media files... ${(progress * 100).toInt()}%');
          }
        },
        onError: (error) {
          if (onError != null) {
            onError!(error);
          }
        },
      );

      final downloadResult = await downloader.downloadMediaFiles(mediaList);
      if (!downloadResult) {
        if (onError != null) {
          onError!('Failed to download media files.');
        }
        return false;
      }

      // Update progress
      if (onProgress != null) {
        onProgress!(0.9, 'Moving files to data directory...');
      }

      // Move files from temp to data directory
      await SupabaseService.moveFilesFromTempToData();

      // Log data update only if requested
      if (logDataUpdate) {
        final loggingService = LoggingService();
        loggingService.logDataUpdate();
      }

      // Update progress
      if (onProgress != null) {
        onProgress!(1.0, 'Data fetch complete.');
      }

      // Complete
      if (onComplete != null) {
        onComplete!();
      }

      return true;
    } catch (e) {
      SupabaseService.debugLog('Error fetching data: $e');

      // Log error
      final loggingService = LoggingService();
      loggingService.logError('Error fetching data: $e');

      if (onError != null) {
        onError!('Error fetching data: $e');
      }
      return false;
    }
  }
}
