import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:signage/core/models/media.dart';
import 'package:signage/core/models/settings.dart';
import 'package:signage/core/services/logging_service.dart';
import 'package:signage/core/services/supabase_service.dart';
import 'package:signage/core/storage/storage_service.dart';

/// Service for downloading media files from Supabase storage
class FileDownloader {
  /// Download progress callback
  final void Function(double progress)? onProgress;

  /// Download complete callback
  final void Function()? onComplete;

  /// Download error callback
  final void Function(String error)? onError;

  /// Constructor
  FileDownloader({
    this.onProgress,
    this.onComplete,
    this.onError,
  });

  /// Download a list of media files
  Future<bool> downloadMediaFiles(List<Media> mediaList) async {
    try {
      final contentDir = Directory('${await StorageService.signageDirectory}/content');

      // Ensure content directory exists
      if (!await contentDir.exists()) {
        await contentDir.create(recursive: true);
      }

      // Total number of files to download
      final totalFiles = mediaList.length;
      int downloadedFiles = 0;

      // Get logging service instance
      final loggingService = LoggingService();

      // Load settings to get screen ID
      final settings = await Settings.load();
      if (settings == null) {
        SupabaseService.debugLog('Error: Settings not found, cannot delete existing download records');
        return false;
      }

      // Delete existing file download records before logging new ones
      final deleteResult = await SupabaseService.deleteFileDownloadRecords(settings.screenId);
      if (!deleteResult) {
        SupabaseService.debugLog('WARNING: Failed to delete existing file download records. Proceeding with caution.');
      } else {
        SupabaseService.debugLog('Successfully deleted existing file download records for screen: ${settings.screenId}');
      }

      // Add a small delay to ensure database operations are completed
      await Future.delayed(const Duration(milliseconds: 500));

      // Download each file
      for (final media in mediaList) {
        try {
          final storagePath = media.storagePath;
          final localPath = '${contentDir.path}/${media.localFileName}';

          // Check if file already exists
          final localFile = File(localPath);
          if (await localFile.exists()) {
            // Skip downloading if file already exists
            SupabaseService.debugLog('File already exists: $localPath');

            // Log file download (even though it was skipped)
            final fileSize = await localFile.length();
            downloadedFiles++;
            loggingService.logFileDownload(
              media.localFileName,
              fileSize,
              downloadedFiles,
              totalFiles
            );

            // Update progress
            if (onProgress != null) {
              onProgress!(downloadedFiles / totalFiles);
            }

            continue;
          }

          // Download file from Supabase storage
          final fileData = await SupabaseService.downloadFile(storagePath);

          if (fileData != null) {
            // Save file to local storage
            await localFile.writeAsBytes(fileData);
            SupabaseService.debugLog('Downloaded file: $localPath');

            // Log file download
            downloadedFiles++;
            loggingService.logFileDownload(
              media.localFileName,
              fileData.length,
              downloadedFiles,
              totalFiles
            );
          } else {
            SupabaseService.debugLog('Failed to download file: $storagePath');
            if (onError != null) {
              onError!('Failed to download file: ${media.name}');
            }

            // Log error
            loggingService.logError('Failed to download file: ${media.name}');
          }
        } catch (e) {
          SupabaseService.debugLog('Error downloading file ${media.name}: $e');
          if (onError != null) {
            onError!('Error downloading file ${media.name}: $e');
          }

          // Log error
          loggingService.logError('Error downloading file ${media.name}: $e');
        }

        // Update progress
        if (onProgress != null) {
          onProgress!(downloadedFiles / totalFiles);
        }
      }

      // All files downloaded
      if (onComplete != null) {
        onComplete!();
      }

      return true;
    } catch (e) {
      SupabaseService.debugLog('Error downloading media files: $e');
      if (onError != null) {
        onError!('Error downloading media files: $e');
      }

      // Log error
      final loggingService = LoggingService();
      loggingService.logError('Error downloading media files: $e');

      return false;
    }
  }

  /// Download a single file from Supabase storage
  Future<File?> downloadSingleFile(Media media) async {
    try {
      final contentDir = Directory('${await StorageService.signageDirectory}/content');

      // Ensure content directory exists
      if (!await contentDir.exists()) {
        await contentDir.create(recursive: true);
      }

      final storagePath = media.storagePath;
      final localPath = '${contentDir.path}/${media.localFileName}';

      // Check if file already exists
      final localFile = File(localPath);
      if (await localFile.exists()) {
        // Return existing file
        SupabaseService.debugLog('File already exists: $localPath');
        return localFile;
      }

      // Download file from Supabase storage
      final fileData = await SupabaseService.downloadFile(storagePath);

      if (fileData != null) {
        // Save file to local storage
        await localFile.writeAsBytes(fileData);
        SupabaseService.debugLog('Downloaded file: $localPath');
        return localFile;
      } else {
        SupabaseService.debugLog('Failed to download file: $storagePath');
        if (onError != null) {
          onError!('Failed to download file: ${media.name}');
        }
        return null;
      }
    } catch (e) {
      SupabaseService.debugLog('Error downloading file ${media.name}: $e');
      if (onError != null) {
        onError!('Error downloading file ${media.name}: $e');
      }
      return null;
    }
  }
}
