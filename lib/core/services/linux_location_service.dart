import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:geolocator/geolocator.dart';

class LinuxLocationService {
  /// Get device location on Linux using IP-based geolocation
  static Future<Position> getLinuxLocation() async {
    try {
      // First try using geoclue-2.0 if available (GNOME's location service)
      final geoclueResult = await _tryGeoclue();
      if (geoclueResult != null) {
        debugPrint('Location obtained from geoclue: ${geoclueResult.latitude}, ${geoclueResult.longitude}');
        return geoclueResult;
      }
      
      // If geoclue fails, fall back to IP-based geolocation
      final ipBasedLocation = await _getLocationFromIP();
      debugPrint('Location obtained from IP: ${ipBasedLocation.latitude}, ${ipBasedLocation.longitude}');
      return ipBasedLocation;
    } catch (e) {
      debugPrint('Error getting Linux location: $e');
      // Fallback to New York coordinates
      return Position(
        longitude: -74.0060,
        latitude: 40.7128,
        timestamp: DateTime.now(),
        accuracy: 0.0,
        altitude: 0.0,
        altitudeAccuracy: 0.0,
        heading: 0.0,
        headingAccuracy: 0.0,
        speed: 0.0,
        speedAccuracy: 0.0,
      );
    }
  }

  /// Try to get location using geoclue-2.0 (GNOME's location service)
  static Future<Position?> _tryGeoclue() async {
    try {
      // Check if geoclue-2.0 is available
      final result = await Process.run('which', ['geoclue-2.0']);
      if (result.exitCode != 0) {
        return null; // geoclue not available
      }
      
      // This is a simplified approach - in a real implementation,
      // you would need to use D-Bus to communicate with geoclue
      // For now, we'll just return null to fall back to IP-based location
      return null;
    } catch (e) {
      debugPrint('Error checking for geoclue: $e');
      return null;
    }
  }

  /// Get location based on IP address using a free geolocation API
  static Future<Position> _getLocationFromIP() async {
    // Use ipinfo.io API (free tier, no API key required for basic usage)
    final response = await http.get(Uri.parse('https://ipinfo.io/json'));
    
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      final locationStr = data['loc'] as String? ?? '40.7128,-74.0060'; // Default to NYC
      final parts = locationStr.split(',');
      
      if (parts.length == 2) {
        final latitude = double.tryParse(parts[0]) ?? 40.7128;
        final longitude = double.tryParse(parts[1]) ?? -74.0060;
        
        return Position(
          longitude: longitude,
          latitude: latitude,
          timestamp: DateTime.now(),
          accuracy: 1000.0, // IP geolocation is not very accurate
          altitude: 0.0,
          altitudeAccuracy: 0.0,
          heading: 0.0,
          headingAccuracy: 0.0,
          speed: 0.0,
          speedAccuracy: 0.0,
        );
      }
    }
    
    // Fallback to New York coordinates
    return Position(
      longitude: -74.0060,
      latitude: 40.7128,
      timestamp: DateTime.now(),
      accuracy: 0.0,
      altitude: 0.0,
      altitudeAccuracy: 0.0,
      heading: 0.0,
      headingAccuracy: 0.0,
      speed: 0.0,
      speedAccuracy: 0.0,
    );
  }
}